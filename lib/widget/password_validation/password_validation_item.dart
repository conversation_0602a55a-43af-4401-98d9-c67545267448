import 'package:flutter/material.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class PasswordValidationItem extends StatelessWidget {
  final String text;
  final bool isValid;

  const PasswordValidationItem({
    super.key,
    required this.text,
    required this.isValid,
  });

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;

    return Row(
      children: [
        // Checkbox
        Container(
          width: 22,
          height: 22,
          decoration: BoxDecoration(
            color: isValid ? themeData.primaryGreen500 : Colors.transparent,
            border: Border.all(
              color: isValid ? themeData.primaryGreen500 : themeData.neutral200,
              width: 1,
            ),
            borderRadius: BorderRadius.circular(6),
          ),
          child: isValid
              ? Icon(
                  Icons.check,
                  size: 14,
                  color: Colors.white,
                )
              : null,
        ),
        const SizedBox(width: 12),
        // Text
        Expanded(
          child: Text(
            text,
            style: bodySmall.copyWith(
              color: themeData.textSecondary,
            ),
          ),
        ),
      ],
    );
  }
}
