import 'package:flutter/material.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/password_validation/password_validation_item.dart';

class PasswordValidationList extends StatelessWidget {
  final bool have8Characters;
  final bool containsUppercase;
  final bool haveSpecialCharacter;

  const PasswordValidationList({
    super.key,
    required this.have8Characters,
    required this.containsUppercase,
    required this.haveSpecialCharacter,
  });

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Your password should contain:',
          style: titleSmall.copyWith(
            color: themeData.textSecondary,
          ),
        ),
        const SizedBox(height: 8),
        Column(
          children: [
            PasswordValidationItem(
              text: '8 or more characters.',
              isValid: have8Characters,
            ),
            const Si<PERSON><PERSON><PERSON>(height: 6),
            PasswordValidationItem(
              text: 'At least one upper case character.',
              isValid: containsUppercase,
            ),
            const SizedBox(height: 6),
            PasswordValidationItem(
              text: 'At least one symbol.',
              isValid: haveSpecialCharacter,
            ),
          ],
        ),
      ],
    );
  }
}
