import 'package:flutter/material.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class AvatarWidget extends StatelessWidget {
  const AvatarWidget({
    super.key,
    required this.name,
    this.imageUrl,
    this.size = 64,
    this.backgroundColor = Colors.white,
    this.nameStyle,
  });

  final String name;
  final String? imageUrl;
  final double size;
  final Color? backgroundColor;
  final TextStyle? nameStyle;

  @override
  Widget build(BuildContext context) {
    Widget avatarWidget;

    if (imageUrl != null && imageUrl!.isNotEmpty) {
      avatarWidget = CircleAvatar(
        radius: size / 2,
        backgroundColor: backgroundColor,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(size / 2),
          child: Image.network(
            imageUrl!,
            width: size,
            height: size,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Center(
                child: Text(
                  name.isNotEmpty ? name[0].toUpperCase() : "",
                  style: titleMedium.copyColor(themeData.neutral800),
                ),
              );
            },
          ),
        ),
      );
    } else {
      avatarWidget = CircleAvatar(
        radius: size / 2,
        backgroundColor: backgroundColor,
        child: Text(
          name.isNotEmpty ? name[0].toUpperCase() : "",
          style: titleMedium.copyColor(themeData.neutral800),
          // style:
          //  nameStyle ??
          //    style:. AppTextStyle.body1Medium.copyWith(color: AppColors.white),
        ),
      );
    }

    return avatarWidget;
  }
}
