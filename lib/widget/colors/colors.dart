import 'package:flutter/material.dart';

extension ThemeExtension on BuildContext {
  ThemeData get themeData => Theme.of(this);
}

extension ColorApp on ThemeData {
  Color get primaryGreen50 {
    if (brightness == Brightness.light) {
      return const Color(0xff82AD0A).withValues(alpha: 0.1);
    } else {
      return const Color(0xff82AD0A).withValues(alpha: 0.1);
    }
  }

  Color get primaryGreen100 {
    if (brightness == Brightness.light) {
      return const Color(0xff82AD0A).withValues(alpha: 0.3);
    } else {
      return const Color(0xff82AD0A).withValues(alpha: 0.3);
    }
  }

  Color get primaryGreen200 {
    if (brightness == Brightness.light) {
      return const Color(0xff82AD0A).withValues(alpha: 0.2);
    } else {
      return const Color(0xff82AD0A).withValues(alpha: 0.2);
    }
  }

  Color get primaryGreen500 {
    if (brightness == Brightness.light) {
      return const Color(0xff82AD0A);
    } else {
      return const Color(0xff82AD0A);
    }
  }

  Color get primaryGreen600 {
    if (brightness == Brightness.light) {
      return const Color(0xff7AA20A);
    } else {
      return const Color(0xff7AA20A);
    }
  }

  Color get primaryGreen900 {
    if (brightness == Brightness.light) {
      return const Color(0xff394C05);
    } else {
      return const Color(0xff394C05);
    }
  }

  Color get white600 {
    if (brightness == Brightness.light) {
      return Colors.white.withValues(alpha: 0.6);
    } else {
      return Colors.white.withValues(alpha: 0.6);
    }
  }

  Color get white700 {
    if (brightness == Brightness.light) {
      return Colors.white.withValues(alpha: 0.7);
    } else {
      return Colors.white.withValues(alpha: 0.7);
    }
  }

  Color get white900 {
    if (brightness == Brightness.light) {
      return Colors.white.withValues(alpha: 0.9);
    } else {
      return Colors.white.withValues(alpha: 0.9);
    }
  }

  Color get schemesOnPrimary {
    if (brightness == Brightness.light) {
      return Colors.white;
    } else {
      return Colors.white;
    }
  }

  Color get white50 {
    if (brightness == Brightness.light) {
      return Colors.white.withValues(alpha: 0.05);
    } else {
      return Colors.white.withValues(alpha: 0.05);
    }
  }

  Color get white200 {
    if (brightness == Brightness.light) {
      return Colors.white.withValues(alpha: 0.2);
    } else {
      return Colors.white.withValues(alpha: 0.2);
    }
  }

  Color get neutral100 {
    if (brightness == Brightness.light) {
      return const Color(0xffF6F6F6);
    } else {
      return const Color(0xffF6F6F6);
    }
  }

  Color get neutral200 {
    if (brightness == Brightness.light) {
      return const Color(0xffE5E5E5);
    } else {
      return const Color(0xffE5E5E5);
    }
  }

  Color get neutral300 {
    if (brightness == Brightness.light) {
      return const Color(0xffAFAFAF);
    } else {
      return const Color(0xffAFAFAF);
    }
  }

  Color get neutral50 {
    if (brightness == Brightness.light) {
      return Colors.white;
    } else {
      return Colors.white;
    }
  }

  Color get black800 {
    if (brightness == Brightness.light) {
      return Colors.black.withValues(alpha: 0.8);
    } else {
      return Colors.black.withValues(alpha: 0.8);
    }
  }

  Color get black600 {
    if (brightness == Brightness.light) {
      return Colors.black.withValues(alpha: 0.6);
    } else {
      return Colors.black.withValues(alpha: 0.6);
    }
  }

  Color get black900 {
    if (brightness == Brightness.light) {
      return Colors.black.withValues(alpha: 0.9);
    } else {
      return Colors.black.withValues(alpha: 0.9);
    }
  }

  Color get black50 {
    if (brightness == Brightness.light) {
      return Colors.black.withValues(alpha: 0.05);
    } else {
      return Colors.black.withValues(alpha: 0.05);
    }
  }

  Color get black400 {
    if (brightness == Brightness.light) {
      return Colors.black.withValues(alpha: 0.4);
    } else {
      return Colors.black.withValues(alpha: 0.4);
    }
  }

  Color get neutral800 {
    if (brightness == Brightness.light) {
      return const Color(0xff292929);
    } else {
      return const Color(0xff292929);
    }
  }

  Color get neutral400 {
    if (brightness == Brightness.light) {
      return const Color(0xff777777);
    } else {
      return const Color(0xff777777);
    }
  }

  Color get neutral500 {
    if (brightness == Brightness.light) {
      return const Color(0xff4B4B4B);
    } else {
      return const Color(0xff4B4B4B);
    }
  }

  Color get red500 {
    if (brightness == Brightness.light) {
      return const Color(0xffD33636);
    } else {
      return const Color(0xffD33636);
    }
  }

  Color get textPrimary {
    if (brightness == Brightness.light) {
      return const Color(0xFF292929); // neutral800
    } else {
      return const Color(0xFF292929);
    }
  }

  Color get textSecondary {
    if (brightness == Brightness.light) {
      return const Color(0xFF777777); // neutral400
    } else {
      return const Color(0xFF777777);
    }
  }

  Color get textDisabled {
    if (brightness == Brightness.light) {
      return const Color(0xFFAFAFAF);
    } else {
      return const Color(0xFFE0E0E0);
    }
  }

  Color get textContrast {
    if (brightness == Brightness.light) {
      return const Color(0xFFFFFFFF);
    } else {
      return const Color(0xFFFFFFFF);
    }
  }
}
