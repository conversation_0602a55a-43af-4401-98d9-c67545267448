import 'package:flutter/material.dart';

class BaseTabBar extends StatefulWidget {
  final List<String> tabTitles; // <PERSON>h sách tiêu đề tab
  final List<Widget> tabContents; // Danh sách nội dung của từng tab
  final Color? tabBarColor; // <PERSON><PERSON>u nền của TabBar
  final Color? selectedTabColor; // <PERSON>àu của tab được chọn
  final Color? unselectedTabColor; // Màu của tab không được chọn
  final TextStyle? tabTextStyle; // Style chữ của tab
  final double? tabBarHeight; // Chiều cao của TabBar
  final double? tabContentHeight; // Chiều cao của TabBarView
  final Function(int)? onTabChanged; // Callback khi tab thay đổi

  const BaseTabBar({
    super.key,
    required this.tabTitles,
    required this.tabContents,
    this.tabBarColor,
    this.selectedTabColor,
    this.unselectedTabColor,
    this.tabTextStyle,
    this.tabBarHeight,
    this.tabContentHeight,
    this.onTabChanged,
  });

  @override
  State<BaseTabBar> createState() => _BaseTabBarState();
}

class _BaseTabBarState extends State<BaseTabBar> {
  int _currentIndex = 0;

  void _handleTabChange(int index) {
    setState(() {
      _currentIndex = index;
    });
    widget.onTabChanged?.call(index);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min, // Chỉ chiếm không gian cần thiết
      children: [
        // TabBar
        Container(
          height: widget.tabBarHeight ?? 48.0,
          color: widget.tabBarColor ?? Colors.transparent,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            children: List.generate(widget.tabTitles.length, (index) {
              final isSelected = index == _currentIndex;
              return GestureDetector(
                onTap: () => _handleTabChange(index),
                child: Padding(
                  padding: const EdgeInsets.only(right: 24),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        widget.tabTitles[index],
                        style:
                            isSelected
                                ? (widget.tabTextStyle ??
                                        const TextStyle(fontSize: 16))
                                    .copyWith(
                                      color:
                                          widget.selectedTabColor ??
                                          Theme.of(context).primaryColor,
                                      fontWeight: FontWeight.bold,
                                    )
                                : (widget.tabTextStyle ??
                                        const TextStyle(fontSize: 16))
                                    .copyWith(
                                      color:
                                          widget.unselectedTabColor ??
                                          Colors.grey,
                                    ),
                      ),
                      const SizedBox(height: 4),
                      if (isSelected)
                        Container(
                          height: 3,
                          width: 28,
                          decoration: BoxDecoration(
                            color:
                                widget.selectedTabColor ??
                                Theme.of(context).primaryColor,
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                    ],
                  ),
                ),
              );
            }),
          ),
        ),
        // Content
        if (widget.tabContentHeight != null)
          SizedBox(
            height: widget.tabContentHeight,
            child: IndexedStack(
              index: _currentIndex,
              children: widget.tabContents,
            ),
          )
        else
          // Flexible height - hiển thị content của tab được chọn
          widget.tabContents[_currentIndex],
      ],
    );
  }
}
