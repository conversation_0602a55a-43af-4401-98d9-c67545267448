import 'package:json_annotation/json_annotation.dart';

part 'repost_request.g.dart';

@JsonSerializable()
class RepostRequest {
  const RepostRequest({
    required this.originalPostId,
    this.repostContent,
    // this.mediaKeys,
    this.privacy = 'public',
    // this.allowedUserIds,
  });

  @Json<PERSON>ey(name: 'original_post_id')
  final String originalPostId;

  @JsonKey(name: 'repost_content')
  final String? repostContent;

  // @JsonKey(name: 'media_keys')
  // final List<String>? mediaKeys;

  final String privacy;

  // @Json<PERSON>ey(name: 'allowed_user_ids')
  // final List<String>? allowedUserIds;

  factory RepostRequest.fromJson(Map<String, dynamic> json) =>
      _$RepostRequestFromJson(json);

  Map<String, dynamic> toJson() => _$RepostRequestToJson(this);
}
