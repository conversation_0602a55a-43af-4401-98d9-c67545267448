import 'package:json_annotation/json_annotation.dart';

part 'username_availability_response_model.g.dart';

@JsonSerializable()
class UsernameAvailabilityResponseModel {
  factory UsernameAvailabilityResponseModel.fromJson(Map<String, dynamic> json) =>
      _$UsernameAvailabilityResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$UsernameAvailabilityResponseModelToJson(this);

  @Json<PERSON>ey(name: 'available')
  final bool available;

  @Json<PERSON>ey(name: 'message')
  final String message;

  @JsonKey(name: 'username')
  final String username;

  const UsernameAvailabilityResponseModel({
    required this.available,
    required this.message,
    required this.username,
  });
}
