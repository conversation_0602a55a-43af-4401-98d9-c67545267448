import 'package:json_annotation/json_annotation.dart';

part 'ai_chat_response_model.g.dart';

@JsonSerializable()
class AiChatResponseModel {
  final String? id;
  final String? object;
  final int? created;
  final String? model;
  final List<AiChatChoiceModel>? choices;
  final AiChatUsageModel? usage;

  const AiChatResponseModel({
    this.id,
    this.object,
    this.created,
    this.model,
    this.choices,
    this.usage,
  });

  factory AiChatResponseModel.fromJson(Map<String, dynamic> json) =>
      _$AiChatResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$AiChatResponseModelToJson(this);
}

@JsonSerializable()
class AiChatChoiceModel {
  final int? index;
  final AiChatMessageResponseModel? message;
  @JsonKey(name: 'finish_reason')
  final String? finishReason;

  const AiChatChoiceModel({this.index, this.message, this.finishReason});

  factory AiChatChoiceModel.fromJson(Map<String, dynamic> json) =>
      _$AiChatChoiceModelFromJson(json);

  Map<String, dynamic> toJson() => _$AiChatChoiceModelToJson(this);
}

@JsonSerializable()
class AiChatMessageResponseModel {
  final String? role;
  final String? content;

  const AiChatMessageResponseModel({this.role, this.content});

  factory AiChatMessageResponseModel.fromJson(Map<String, dynamic> json) =>
      _$AiChatMessageResponseModelFromJson(json);

  Map<String, dynamic> toJson() => _$AiChatMessageResponseModelToJson(this);
}

@JsonSerializable()
class AiChatUsageModel {
  @JsonKey(name: 'prompt_tokens')
  final int? promptTokens;
  @JsonKey(name: 'completion_tokens')
  final int? completionTokens;
  @JsonKey(name: 'total_tokens')
  final int? totalTokens;

  const AiChatUsageModel({
    this.promptTokens,
    this.completionTokens,
    this.totalTokens,
  });

  factory AiChatUsageModel.fromJson(Map<String, dynamic> json) =>
      _$AiChatUsageModelFromJson(json);

  Map<String, dynamic> toJson() => _$AiChatUsageModelToJson(this);
}
