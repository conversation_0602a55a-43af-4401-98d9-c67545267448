import 'package:json_annotation/json_annotation.dart';

part 'ai_chat_request_model.g.dart';

@JsonSerializable()
class AiChatRequestModel {
  final List<AiChatMessageModel>? messages;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'include_reasoning')
  final bool includeReasoning;
  final bool stream;

  const AiChatRequestModel({
    this.messages,
    this.includeReasoning = false,
    this.stream = false,
  });

  factory AiChatRequestModel.fromJson(Map<String, dynamic> json) =>
      _$AiChatRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$AiChatRequestModelToJson(this);
}

@JsonSerializable()
class AiChatMessageModel {
  final String? role;
  final String? content;

  const AiChatMessageModel({this.role, this.content});

  factory AiChatMessageModel.fromJson(Map<String, dynamic> json) =>
      _$AiChatMessageModelFromJson(json);

  Map<String, dynamic> toJson() => _$AiChatMessageModelToJson(this);
}
