import 'package:share_plus/share_plus.dart';
import 'package:toii_social/core/service/deep_link_service.dart';
import 'package:toii_social/model/post/post_model.dart';

class ShareService {
  // Cache for share text to avoid rebuilding
  static final Map<String, String> _shareTextCache = {};

  /// Share a post with text and link
  static Future<ShareResult> sharePost(PostModel post) async {
    try {
      final String postUrl = DeepLinkService.generatePostLink(post.id);
      final String shareText = _getCachedShareText(post, postUrl);

      return await SharePlus.instance.share(
        ShareParams(
          text: shareText,
          subject: 'Check out this post on Gao Social',
        ),
      );
    } catch (e) {
      throw Exception('Failed to share post: $e');
    }
  }

  /// Share post with custom text
  static Future<ShareResult> sharePostWithText(
    PostModel post,
    String customText,
  ) async {
    try {
      final String postUrl = DeepLinkService.generatePostLink(post.id);
      final String shareText = '$customText\n\n$postUrl';

      return await SharePlus.instance.share(
        ShareParams(
          text: shareText,
          subject: 'Check out this post on Gao Social',
        ),
      );
    } catch (e) {
      throw Exception('Failed to share post: $e');
    }
  }

  /// Share post to specific app (if supported)
  static Future<ShareResult> sharePostToApp(
    PostModel post,
    String packageName,
  ) async {
    try {
      final String postUrl = DeepLinkService.generatePostLink(post.id);
      final String shareText = _buildShareText(post, postUrl);

      // Note: share_plus doesn't directly support sharing to specific apps
      // This would require platform-specific implementation
      return await SharePlus.instance.share(
        ShareParams(
          text: shareText,
          subject: 'Check out this post on Gao Social',
        ),
      );
    } catch (e) {
      throw Exception('Failed to share post to app: $e');
    }
  }

  /// Get cached share text or build new one
  static String _getCachedShareText(PostModel post, String postUrl) {
    final cacheKey = '${post.id}_${post.content.hashCode}';

    if (_shareTextCache.containsKey(cacheKey)) {
      return _shareTextCache[cacheKey]!;
    }

    final shareText = _buildShareText(post, postUrl);
    _shareTextCache[cacheKey] = shareText;

    // Limit cache size to prevent memory leaks
    if (_shareTextCache.length > 50) {
      _shareTextCache.clear();
    }

    return shareText;
  }

  /// Build share text from post content
  static String _buildShareText(PostModel post, String postUrl) {
    final String authorName =
        post.user?.fullName ?? post.user?.username ?? 'Someone';
    final String content =
        post.content.isNotEmpty
            ? post.content.length > 100
                ? '${post.content.substring(0, 100)}...'
                : post.content
            : 'Check out this post';

    return '''$authorName shared on Gao Social:

"$content"

$postUrl

#GaoSocial #Share''';
  }

  /// Share just the post URL
  static Future<ShareResult> sharePostUrl(PostModel post) async {
    try {
      final String postUrl = DeepLinkService.generatePostLink(post.id);
      return await SharePlus.instance.share(ShareParams(text: postUrl));
    } catch (e) {
      throw Exception('Failed to share post URL: $e');
    }
  }

  /// Share with files (if post has media)
  static Future<ShareResult> sharePostWithMedia(
    PostModel post,
    List<XFile> files,
  ) async {
    try {
      final String postUrl = DeepLinkService.generatePostLink(post.id);
      final String shareText = _buildShareText(post, postUrl);

      return await SharePlus.instance.share(
        ShareParams(
          text: shareText,
          files: files,
          subject: 'Check out this post on Gao Social',
        ),
      );
    } catch (e) {
      throw Exception('Failed to share post with media: $e');
    }
  }
}
