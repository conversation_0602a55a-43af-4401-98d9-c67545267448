import 'package:go_router/go_router.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/utils/navigation_service.dart';

class DeepLinkService {
  static const String _appScheme = 'toii';
  static const String _webBaseUrl = 'https://gao.social';

  /// Generate deep link for a post
  static String generatePostLink(String postId) {
    return '$_webBaseUrl/post/$postId';
  }

  /// Generate app link for a post
  static String generatePostAppLink(String postId) {
    return '$_appScheme://post/$postId';
  }

  /// Generate user profile link
  static String generateUserLink(String userId) {
    return '$_webBaseUrl/user/$userId';
  }

  /// Generate user profile app link
  static String generateUserAppLink(String userId) {
    return '$_appScheme://user/$userId';
  }

  /// Handle incoming deep link
  static Future<bool> handleDeepLink(String link) async {
    try {
      final Uri uri = Uri.parse(link);

      // Handle web links
      if (uri.host == 'gao.social') {
        return await _handleWebLink(uri);
      }

      // Handle app scheme links
      if (uri.scheme == _appScheme) {
        return await _handleAppLink(uri);
      }

      return false;
    } catch (e) {
      print('Error handling deep link: $e');
      return false;
    }
  }

  /// Handle web links (https://gao.social/...)
  static Future<bool> _handleWebLink(Uri uri) async {
    final pathSegments = uri.pathSegments;

    if (pathSegments.isEmpty) {
      // Navigate to home
      getContext.go(RouterEnums.mainTabbar.routeName);
      return true;
    }

    switch (pathSegments[0]) {
      case 'post':
        if (pathSegments.length > 1) {
          final postId = pathSegments[1];
          return await _navigateToPost(postId);
        }
        break;

      case 'user':
        if (pathSegments.length > 1) {
          final userId = pathSegments[1];
          return await _navigateToUser(userId);
        }
        break;

      default:
        // Navigate to home for unknown paths
        getContext.go(RouterEnums.mainTabbar.routeName);
        return true;
    }

    return false;
  }

  /// Handle app scheme links (toii://...)
  static Future<bool> _handleAppLink(Uri uri) async {
    final pathSegments = uri.pathSegments;

    if (pathSegments.isEmpty) {
      getContext.go(RouterEnums.mainTabbar.routeName);
      return true;
    }

    switch (pathSegments[0]) {
      case 'post':
        if (pathSegments.length > 1) {
          final postId = pathSegments[1];
          return await _navigateToPost(postId);
        }
        break;

      case 'user':
        if (pathSegments.length > 1) {
          final userId = pathSegments[1];
          return await _navigateToUser(userId);
        }
        break;
    }

    return false;
  }

  /// Navigate to post detail screen
  static Future<bool> _navigateToPost(String postId) async {
    try {
      // Navigate to post detail screen
      // You'll need to implement this route in your router
      getContext.go('/post/$postId');
      return true;
    } catch (e) {
      print('Error navigating to post: $e');
      return false;
    }
  }

  /// Navigate to user profile screen
  static Future<bool> _navigateToUser(String userId) async {
    try {
      // Navigate to user profile screen
      // You'll need to implement this route in your router
      getContext.go('/user/$userId');
      return true;
    } catch (e) {
      print('Error navigating to user: $e');
      return false;
    }
  }

  /// Check if a link is a Toii Social link
  static bool isToiiLink(String link) {
    try {
      final Uri uri = Uri.parse(link);
      return uri.host == 'gao.social' || uri.scheme == _appScheme;
    } catch (e) {
      return false;
    }
  }

  /// Extract post ID from link
  static String? extractPostId(String link) {
    try {
      final Uri uri = Uri.parse(link);
      final pathSegments = uri.pathSegments;

      if (pathSegments.length >= 2 && pathSegments[0] == 'post') {
        return pathSegments[1];
      }

      return null;
    } catch (e) {
      return null;
    }
  }

  /// Extract user ID from link
  static String? extractUserId(String link) {
    try {
      final Uri uri = Uri.parse(link);
      final pathSegments = uri.pathSegments;

      if (pathSegments.length >= 2 && pathSegments[0] == 'user') {
        return pathSegments[1];
      }

      return null;
    } catch (e) {
      return null;
    }
  }
}
