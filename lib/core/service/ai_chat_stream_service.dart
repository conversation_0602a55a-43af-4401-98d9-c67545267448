import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:toii_social/core/service/stream/sse_transformer.dart';
import 'package:toii_social/model/ai_chat/ai_chat_request_model.dart';

/// Service for handling streaming AI chat responses
class AiChatStreamService {
  final Dio _dio;
  CancelToken? _cancelToken;

  AiChatStreamService(this._dio);

  /// Stream transformer for converting Uint8List to List of int
  final _uint8Transformer =
      StreamTransformer<Uint8List, List<int>>.fromHandlers(
        handleData: (data, sink) {
          sink.add(List<int>.from(data));
        },
      );

  /// Send a streaming chat message request
  Future<Stream<String>> sendStreamingMessage(
    AiChatRequestModel request,
    String authorization,
  ) async {
    // Cancel any existing request
    await cancel();

    // Create new cancel token
    _cancelToken = CancelToken();

    try {
      // Make the streaming request
      final response = await _dio.post<ResponseBody>(
        '/chat/completions',
        data: request.copyWith(stream: true).toJson(),
        options: Options(
          responseType: ResponseType.stream,
          headers: {
            'Authorization': authorization,
            'Content-Type': 'application/json',
          },
        ),
        cancelToken: _cancelToken,
      );

      if (response.data?.stream == null) {
        throw Exception('No stream data received');
      }

      // Transform the stream
      return response.data!.stream
          .transform(_uint8Transformer)
          .transform(const Utf8Decoder())
          .transform(const LineSplitter())
          .transform(const SseTransformer())
          .transform(const ContentTransformer());
    } catch (e) {
      if (e is DioException && e.type == DioExceptionType.cancel) {
        throw Exception('Request was cancelled');
      }
      rethrow;
    }
  }

  /// Cancel the current streaming request
  Future<void> cancel() async {
    _cancelToken?.cancel();
    _cancelToken = null;
  }

  /// Dispose resources
  void dispose() {
    cancel();
  }
}

/// Extension to add copyWith method to AiChatRequestModel
extension AiChatRequestModelExtension on AiChatRequestModel {
  AiChatRequestModel copyWith({
    List<AiChatMessageModel>? messages,
    bool? includeReasoning,
    bool? stream,
  }) {
    return AiChatRequestModel(
      messages: messages ?? this.messages,
      includeReasoning: includeReasoning ?? this.includeReasoning,
      stream: stream ?? this.stream,
    );
  }
}
