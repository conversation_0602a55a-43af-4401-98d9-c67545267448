import 'dart:async';
import 'dart:convert';

/// Transformer for Server-Sent Events (SSE) data
class SseTransformer extends StreamTransformerBase<String, String> {
  const SseTransformer();

  @override
  Stream<String> bind(Stream<String> stream) {
    return stream
        .where((line) => line.trim().isNotEmpty)
        .where((line) => line.startsWith('data: '))
        .map((line) => line.substring(6)) // Remove 'data: ' prefix
        .where((data) => data.trim() != '[DONE]'); // Filter out completion marker
  }
}

/// Transformer for extracting content from OpenAI streaming response
class ContentTransformer extends StreamTransformerBase<String, String> {
  const ContentTransformer();

  @override
  Stream<String> bind(Stream<String> stream) {
    return stream.asyncMap((data) async {
      try {
        final json = jsonDecode(data);
        final choices = json['choices'] as List?;
        if (choices != null && choices.isNotEmpty) {
          final delta = choices[0]['delta'];
          if (delta != null && delta['content'] != null) {
            return delta['content'] as String;
          }
        }
      } catch (e) {
        // Ignore parsing errors for malformed chunks
      }
      return '';
    }).where((content) => content.isNotEmpty);
  }
}
