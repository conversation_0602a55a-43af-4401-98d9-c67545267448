import 'package:dio/dio.dart';
import 'package:retrofit/retrofit.dart';
import 'package:toii_social/model/ai_chat/ai_chat_request_model.dart';
import 'package:toii_social/model/ai_chat/ai_chat_response_model.dart';

part 'ai_chat_service.g.dart';

@RestApi()
abstract class AiChatService {
  factory AiChatService(Dio dio, {String baseUrl}) = _AiChatService;

  @POST('https://api.runpod.ai/v2/5h8enyh0q8gm56/openai/v1/chat/completions')
  Future<AiChatResponseModel> sendMessage(
    @Body() AiChatRequestModel request,
    @Header('Authorization') String authorization,
  );
}
