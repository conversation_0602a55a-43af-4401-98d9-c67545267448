abstract class ChainApiRepository {
  //Future<List<ChainModel>> getAll<PERSON>hain();
  // Future<List<TokenItemModel>> getTokenByChain(
  //   String chainId,
  // );
  // Future<List<TokenItemModel>> getTokenLocalByChain(
  //   String chainId,
  // );
  // void setTokenNewToLocal(
  //   List<TokenItemModel> listsAdd,
  //   String chainId,
  // );
  // void addNewCustomToken(
  //   TokenItemModel token,
  // );
}

const List<int> topChainIds = [
  1,
  56,
  137,
  10,
  42161,
  43114,
  8453,
  250,
  324,
  1101,
  59144,
  5000,
  204,
  100,
  1088,
  1284,
  42220,
  9001,
  888,
  81457,
];

const Map<int, String> chainIcons = {
  1: "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/ethereum/info/logo.png",
  56: "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/smartchain/info/logo.png",
  137: "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/polygon/info/logo.png",
  10: "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/optimism/info/logo.png",
  42161: "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/arbitrum/info/logo.png",
  43114: "https://static.particle.network/token-list/avalanche/native.png",
  8453: "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/base/info/logo.png",
  250: "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/fantom/info/logo.png",
  324: "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/zksync/info/logo.png",
  1101: "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/polygonzkevm/info/logo.png",
  59144: "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/linea/info/logo.png",
  5000: "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/mantle/info/logo.png",
  204: "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/opbnb/info/logo.png",
  100: "https://static.particle.network/token-list/gnosis/native.png",
  1088: "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/metis/info/logo.png",
  1284: "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/moonbeam/info/logo.png",
  42220: "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/celo/info/logo.png",
  9001: "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/evmos/info/logo.png",
  888: "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/wanchain/info/logo.png",
  81457: "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/blast/info/logo.png"
};

class ChainApiRepositoryImpl implements ChainApiRepository {
  final tokenChainKey = "KEY_TOKENCHAIN";
  ChainApiRepositoryImpl();

  // @override
  // Future<List<ChainModel>> getAllChain() async {
  //   final String response = await rootBundle.loadString(
  //     'assets/json/chainlist.jchianson',
  //   );

  //   final List<dynamic> data = jsonDecode(response);

  //   final filtered =
  //       data
  //           .where((json) {
  //             final name = json['name']?.toString().trim() ?? '';
  //             final rpc = (json['rpc'] ?? []) as List;
  //             return topChainNames.contains(name) && rpc.isNotEmpty;
  //           })
  //           .map((json) => ChainModel.fromJson(json))
  //           .toList();
  //   return filtered;
  // }

  // @override
  // Future<List<TokenItemModel>> getTokenByChain(
  //   String chainId,
  // ) async {
  //   return await chainApiService.getTokenByChain(chainId);
  // }

  // @override
  // Future<List<TokenItemModel>> getTokenLocalByChain(
  //   String chainId,
  // ) async {
  //   final listTokenStr =
  //       GetIt.instance<SharedPreferencesManager>().getString(tokenChainKey) ??
  //           "";
  //   Map<String, dynamic> tokenJson =
  //       listTokenStr.isEmpty ? {} : json.decode(listTokenStr);
  //   if (tokenJson.isEmpty || tokenJson[chainId] == null) {
  //     return [];
  //   }
  //   List<TokenItemModel> lists = [];

  //   final List<dynamic> jsonToken = tokenJson[chainId] ?? [];
  //   for (var element in jsonToken) {
  //     final token = TokenItemModel.fromJson(element);
  //     if (token.isHidden == false) {
  //       lists.add(token);
  //     }
  //   }

  //   return lists;
  // }

  // @override
  // void setTokenNewToLocal(
  //   List<TokenItemModel> listsAdd,
  //   String chainId,
  // ) {
  //   final listTokenStr =
  //       GetIt.instance<SharedPreferencesManager>().getString(tokenChainKey) ??
  //           "";
  //   Map<String, dynamic> tokenJson =
  //       listTokenStr.isEmpty ? {} : json.decode(listTokenStr);
  //   final List<dynamic> jsonToken = tokenJson[chainId] ?? [];
  //   for (var token in listsAdd) {
  //     bool isAdded = false;
  //     for (var element in jsonToken) {
  //       final token = TokenItemModel.fromJson(element);
  //       if (token.address == token.address) {
  //         isAdded = true;
  //         break;
  //       }
  //     }
  //     if (!isAdded) {
  //       jsonToken.add(token.toJson());
  //     }
  //   }
  //   tokenJson[chainId] = jsonToken;
  //   GetIt.instance<SharedPreferencesManager>()
  //       .putString(tokenChainKey, json.encode(tokenJson));
  // }

  // @override
  // void addNewCustomToken(
  //   TokenItemModel token,
  // ) {
  //   final listTokenStr =
  //       GetIt.instance<SharedPreferencesManager>().getString(tokenChainKey) ??
  //           "";
  //   Map<String, dynamic> tokenJson =
  //       listTokenStr.isEmpty ? {} : json.decode(listTokenStr);
  //   final currentActiveChain = GetIt.instance<WalletCubit>().currentActiveChain;

  //   final List<dynamic> jsonToken = tokenJson[currentActiveChain.chainId] ?? [];

  //   tokenJson[currentActiveChain.chainId!] = jsonToken;
  //   bool isAdded = false;
  //   for (var element in jsonToken) {
  //     final token1 = TokenItemModel.fromJson(element);
  //     if (token1.address == token.address) {
  //       isAdded = true;
  //       break;
  //     }
  //   }
  //   if (!isAdded) {
  //     jsonToken.add(token.toJson());
  //     tokenJson[currentActiveChain.chainId!] = jsonToken;
  //     GetIt.instance<SharedPreferencesManager>()
  //         .putString(tokenChainKey, json.encode(tokenJson));
  //   }
  // }
}
