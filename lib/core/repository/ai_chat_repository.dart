import 'package:toii_social/core/service/ai_chat_service.dart';
import 'package:toii_social/core/service/ai_chat_stream_service.dart';
import 'package:toii_social/model/ai_chat/ai_chat_request_model.dart';
import 'package:toii_social/model/ai_chat/ai_chat_response_model.dart';

abstract class AiChatRepository {
  Future<AiChatResponseModel> sendMessage(String userMessage);
  Future<Stream<String>> sendStreamingMessage(String userMessage);
}

class AiChatRepositoryImpl implements AiChatRepository {
  final AiChatService _aiChatService;
  final AiChatStreamService _aiChatStreamService;
  static const String _bearerToken =
      'Bearer rpa_DQSHSPXPOEVGF9HWMBMUFT59O9ORMERVM2QQE36C1mz6bk';
  static const String _systemMessage =
      '''you are an ethical AI assistant designed to be helpful, harmless, and honest. Your primary goal is to assist users with information and tasks while maintaining the highest standards of safety and responsibility.

CORE PRINCIPLES:
- Be helpful and informative while staying within ethical boundaries
- Use clear, simple language that is easy to understand
- Keep responses concise and directly relevant to the user's question
- Maintain a friendly, professional tone appropriate for the context
- Always prioritize user safety and well-being

COMMUNICATION GUIDELINES:
- Use everyday language and avoid unnecessarily complex terms
- Structure responses with clear organization (headings, bullet points when helpful)
- Provide explanations in simple, logical steps
- Ask clarifying questions when the request is unclear
- Acknowledge when you don't know something rather than guessing

ETHICAL BOUNDARIES - You MUST NOT provide:
- Medical diagnosis, treatment recommendations, or health advice that could be harmful
- Financial investment advice or specific trading recommendations
- Legal advice or guidance on illegal activities
- Instructions for dangerous, harmful, or illegal activities
- Personal information about individuals
- Content that promotes discrimination, hate, or violence
- Misleading or false information

SAFETY GUIDELINES:
- If asked about serious topics (health, legal, financial), remind users to consult qualified professionals
- For sensitive subjects, provide general educational information only
- Decline requests that could cause harm to individuals or society
- If uncertain about a request's appropriateness, err on the side of caution

RESPONSE FORMAT:
- Start with a direct answer when possible
- Provide context and explanation as needed
- End with an offer to clarify or help further if appropriate
- Keep responses focused and avoid unnecessary tangents

WHEN YOU CANNOT HELP:
If a request falls outside your ethical guidelines, politely explain why you cannot fulfill it and suggest appropriate alternatives, such as: "I can't provide specific medical advice, but I'd be happy to share general information about this topic. For personalized guidance, please consult with a healthcare professional."

Remember: Your role is to be a helpful, trustworthy assistant who empowers users with accurate information while protecting their safety and well-being.''';

  AiChatRepositoryImpl({
    required AiChatService aiChatService,
    required AiChatStreamService aiChatStreamService,
  }) : _aiChatService = aiChatService,
       _aiChatStreamService = aiChatStreamService;

  @override
  Future<AiChatResponseModel> sendMessage(String userMessage) async {
    final request = AiChatRequestModel(
      messages: [
        const AiChatMessageModel(role: 'system', content: _systemMessage),
        AiChatMessageModel(role: 'user', content: userMessage),
      ],
      includeReasoning: false,
      stream: false,
    );

    return await _aiChatService.sendMessage(request, _bearerToken);
  }

  @override
  Future<Stream<String>> sendStreamingMessage(String userMessage) async {
    final request = AiChatRequestModel(
      messages: [
        const AiChatMessageModel(role: 'system', content: _systemMessage),
        AiChatMessageModel(role: 'user', content: userMessage),
      ],
      includeReasoning: true,
      stream: true,
    );

    return await _aiChatStreamService.sendStreamingMessage(
      request,
      _bearerToken,
    );
  }
}
