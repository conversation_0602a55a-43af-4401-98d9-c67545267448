import 'package:curl_logger_dio_interceptor/curl_logger_dio_interceptor.dart';
import 'package:dio/dio.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/ai_chat_repository.dart';
import 'package:toii_social/core/repository/auth_repository.dart';
import 'package:toii_social/core/repository/chain_api_repository.dart';
import 'package:toii_social/core/repository/chain_repository.dart';
import 'package:toii_social/core/repository/coin_market_repository.dart';
import 'package:toii_social/core/repository/report_repository.dart';
import 'package:toii_social/core/repository/social_repository.dart';
import 'package:toii_social/core/repository/user_repository.dart';
import 'package:toii_social/core/service/ai_chat_service.dart';
import 'package:toii_social/core/service/ai_chat_stream_service.dart';
import 'package:toii_social/core/service/auth_service.dart';
import 'package:toii_social/core/service/coin_market_price_service.dart';
import 'package:toii_social/core/service/report_service.dart';
import 'package:toii_social/core/service/social_service.dart';
import 'package:toii_social/core/service/user_service.dart';
import 'package:toii_social/cubit/ai_chat/ai_chat_cubit.dart';
import 'package:toii_social/cubit/auth/delete_account/delete_account_cubit.dart';
import 'package:toii_social/cubit/auth/logout/logout_cubit.dart';
import 'package:toii_social/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_social/cubit/follower/follower_cubit.dart';
import 'package:toii_social/cubit/post/home/<USER>';
import 'package:toii_social/cubit/report/report_cubit.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/cubit/user/avatar_upload/avatar_upload_cubit.dart';
import 'package:toii_social/cubit/user/user_stats/user_stats_cubit.dart';
import 'package:toii_social/cubit/wallet/wallet_cubit.dart';
import 'package:toii_social/utils/interceptor/log_api_intercepter.dart';
import 'package:toii_social/utils/interceptor/token_interceptor.dart';
import 'package:toii_social/utils/navigation_service.dart';
import 'package:toii_social/utils/shared_prefs/shared_prefs.dart';

GetIt serviceLocator = GetIt.instance;
Future setupLocator() async {
  await SharedPref.getInstance();

  ///Setup Navigation Service
  serviceLocator.registerLazySingleton(() => NavigationService());

  serviceLocator.registerLazySingleton(() => ThemeCubit());

  final Dio dio = await setupDio("https://api-dev.toii.social");
  serviceLocator.registerLazySingleton<Dio>(() => dio);

  // Setup AI Chat Dio with different base URL
  final Dio aiChatDio = await setupAiChatDio(
    "https://api.runpod.ai/v2/5h8enyh0q8gm56/openai/v1",
  );
  serviceLocator.registerLazySingleton<Dio>(
    () => aiChatDio,
    instanceName: 'aiChatDio',
  );

  // serviceLocator.registerSingleton<WalletConnectorService>(MetamaskConnectorImpl());

  /// Service
  serviceLocator.registerLazySingleton(() => AuthService(serviceLocator()));
  serviceLocator.registerLazySingleton(() => SocialService(serviceLocator()));
  serviceLocator.registerLazySingleton(() => ReportService(serviceLocator()));
  serviceLocator.registerLazySingleton(() => UserService(serviceLocator()));
  serviceLocator.registerLazySingleton(
    () => AiChatService(serviceLocator(instanceName: 'aiChatDio')),
  );
  serviceLocator.registerLazySingleton(
    () => AiChatStreamService(serviceLocator(instanceName: 'aiChatDio')),
  );

  serviceLocator.registerLazySingleton<AuthRepository>(
    () => AuthRepositoryImpl(authService: serviceLocator()),
  );
  serviceLocator.registerLazySingleton<ChainApiRepository>(
    () => ChainApiRepositoryImpl(),
  );

  serviceLocator.registerLazySingleton<SocialRepository>(
    () => SocialRepositoryImpl(socialService: serviceLocator()),
  );

  serviceLocator.registerLazySingleton<ReportRepository>(
    () => ReportRepositoryImpl(reportService: serviceLocator()),
  );

  serviceLocator.registerLazySingleton<UserRepository>(
    () => UserRepositoryImpl(userService: serviceLocator()),
  );

  serviceLocator.registerLazySingleton<AiChatRepository>(
    () => AiChatRepositoryImpl(
      aiChatService: serviceLocator(),
      aiChatStreamService: serviceLocator(),
    ),
  );
  //Chain
  serviceLocator.registerLazySingleton(
    () => CoinMarketPriceService(
      serviceLocator(),
      baseUrl: "https://pro-api.coinmarketcap.com/v1",
    ),
  );
  serviceLocator.registerLazySingleton<CoinMarketRepository>(
    () => CoinMarketRepositoryImpl(coinMarketPriceService: serviceLocator()),
  );

  // final mlxFlutterPlugin = MlxFlutter();
  // serviceLocator.registerLazySingleton(() => mlxFlutterPlugin);

  // cubit
  serviceLocator.registerLazySingleton(() => HomeCubit());
  serviceLocator.registerLazySingleton(() => ReportCubit());
  serviceLocator.registerLazySingleton(() => FollowerCubit());
  serviceLocator.registerLazySingleton(() => UserStatsCubit());
  serviceLocator.registerLazySingleton(() => AvatarUploadCubit());

  serviceLocator.registerLazySingleton<ChainRepository>(
    () => ChainRepositoryImpl(),
  );
  serviceLocator.registerLazySingleton(() => ProfileCubit());
  serviceLocator.registerLazySingleton(() => LogoutCubit());
  serviceLocator.registerLazySingleton(() => DeleteAccountCubit());
  serviceLocator.registerLazySingleton(() => WalletCubit());

  // AI Chat Cubit
  serviceLocator.registerFactory(
    () => AiChatCubit(aiChatRepository: serviceLocator()),
  );
}

Future<Dio> setupDio(String baseUrl) async {
  final options = BaseOptions(
    receiveDataWhenStatusError: true,
    connectTimeout: const Duration(seconds: 60),
    receiveTimeout: const Duration(seconds: 60),
    responseType: ResponseType.json,
    baseUrl: baseUrl,
  );
  final Dio dio = Dio(options);

  dio.interceptors.add(TokenInterceptor());
  dio.interceptors.add(LogAPIInterceptor());

  dio.interceptors.add(CurlLoggerDioInterceptor(printOnSuccess: true));
  dio.interceptors.add(LogInterceptor(requestBody: true, responseBody: true));
  return dio;
}

Future<Dio> setupAiChatDio(String baseUrl) async {
  final options = BaseOptions(
    receiveDataWhenStatusError: true,
    connectTimeout: const Duration(seconds: 300), // 5 minutes for streaming
    receiveTimeout: const Duration(seconds: 300), // 5 minutes for streaming
    responseType: ResponseType.json,
    baseUrl: baseUrl,
  );
  final Dio dio = Dio(options);

  // Add logging interceptors for debugging
  dio.interceptors.add(CurlLoggerDioInterceptor(printOnSuccess: true));
  dio.interceptors.add(LogInterceptor(requestBody: true, responseBody: true));
  return dio;
}
