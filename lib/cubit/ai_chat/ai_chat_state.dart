part of 'ai_chat_cubit.dart';

enum AiChatStatus { initial, loading, streaming, success, failure }

class AiChatState extends Equatable {
  final AiChatStatus status;
  final List<types.Message> messages;
  final String? errorMessage;
  final String?
  streamingMessageId; // ID of the message currently being streamed

  const AiChatState({
    this.status = AiChatStatus.initial,
    this.messages = const [],
    this.errorMessage,
    this.streamingMessageId,
  });

  AiChatState copyWith({
    AiChatStatus? status,
    List<types.Message>? messages,
    String? errorMessage,
    String? streamingMessageId,
  }) {
    return AiChatState(
      status: status ?? this.status,
      messages: messages ?? this.messages,
      errorMessage: errorMessage,
      streamingMessageId: streamingMessageId,
    );
  }

  @override
  List<Object?> get props => [
    status,
    messages,
    errorMessage,
    streamingMessageId,
  ];
}
