import 'dart:async';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_chat_types/flutter_chat_types.dart' as types;
import 'package:toii_social/core/repository/ai_chat_repository.dart';
import 'package:uuid/uuid.dart';

part 'ai_chat_state.dart';

class AiChatCubit extends Cubit<AiChatState> {
  final AiChatRepository _aiChatRepository;
  final _uuid = const Uuid();
  StreamSubscription<String>? _streamSubscription;

  // Create users
  final types.User _user = const types.User(id: 'user', firstName: 'You');

  final types.User _assistant = const types.User(
    id: 'assistant',
    firstName: 'Gao',
  );

  AiChatCubit({required AiChatRepository aiChatRepository})
    : _aiChatRepository = aiChatRepository,
      super(const AiChatState());

  void addMessage(types.Message message) {
    final messages = [...state.messages];
    messages.insert(0, message);
    emit(state.copyWith(status: AiChatStatus.success, messages: messages));
  }

  Future<void> sendMessage(String text) async {
    // Cancel any existing stream
    await _cancelCurrentStream();

    // Add user message
    final userMessage = types.TextMessage(
      author: _user,
      createdAt: DateTime.now().millisecondsSinceEpoch,
      id: _uuid.v4(),
      text: text,
    );

    addMessage(userMessage);

    // Create initial AI message for streaming
    final aiMessageId = _uuid.v4();
    final aiMessage = types.TextMessage(
      author: _assistant,
      createdAt: DateTime.now().millisecondsSinceEpoch,
      id: aiMessageId,
      text: '', // Start with empty text
    );

    // Add empty AI message and set streaming state
    final messages = [...state.messages];
    messages.insert(0, aiMessage);
    emit(
      state.copyWith(
        status: AiChatStatus.streaming,
        messages: messages,
        streamingMessageId: aiMessageId,
        errorMessage: null,
      ),
    );

    try {
      // Start streaming
      final stream = await _aiChatRepository.sendStreamingMessage(text);

      String accumulatedText = '';
      _streamSubscription = stream.listen(
        (chunk) {
          accumulatedText += chunk;
          _updateStreamingMessage(aiMessageId, accumulatedText);
        },
        onDone: () {
          emit(
            state.copyWith(
              status: AiChatStatus.success,
              streamingMessageId: null,
            ),
          );
          _streamSubscription = null;
        },
        onError: (error) {
          _handleStreamError(error);
        },
      );
    } catch (error) {
      _handleStreamError(error);
    }
  }

  void _updateStreamingMessage(String messageId, String text) {
    final messages = [...state.messages];
    final messageIndex = messages.indexWhere((msg) => msg.id == messageId);

    if (messageIndex != -1) {
      final oldMessage = messages[messageIndex] as types.TextMessage;
      messages[messageIndex] = oldMessage.copyWith(text: text);
      emit(state.copyWith(messages: messages));
    }
  }

  void _handleStreamError(dynamic error) {
    // Replace the streaming message with an error message
    final messages = [...state.messages];
    if (state.streamingMessageId != null) {
      final messageIndex = messages.indexWhere(
        (msg) => msg.id == state.streamingMessageId,
      );
      if (messageIndex != -1) {
        final errorMessage = types.TextMessage(
          author: _assistant,
          createdAt: DateTime.now().millisecondsSinceEpoch,
          id: state.streamingMessageId!,
          text: 'Sorry, I encountered an error. Please try again.',
        );
        messages[messageIndex] = errorMessage;
      }
    }

    emit(
      state.copyWith(
        status: AiChatStatus.failure,
        messages: messages,
        errorMessage: error.toString(),
        streamingMessageId: null,
      ),
    );
    _streamSubscription = null;
  }

  Future<void> _cancelCurrentStream() async {
    await _streamSubscription?.cancel();
    _streamSubscription = null;
  }

  types.User get user => _user;
  types.User get assistant => _assistant;

  @override
  Future<void> close() async {
    await _cancelCurrentStream();
    return super.close();
  }
}
