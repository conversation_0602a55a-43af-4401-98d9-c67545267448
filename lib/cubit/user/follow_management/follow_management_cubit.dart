import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/user_repository.dart';
import 'package:toii_social/model/follower/follower_model.dart';

part 'follow_management_state.dart';

class FollowManagementCubit extends Cubit<FollowManagementState> {
  FollowManagementCubit({
    required String userId,
    bool initialFollowingStatus = false,
  }) : super(FollowManagementState(
          userId: userId,
          isFollowing: initialFollowingStatus,
        ));

  final UserRepository _userRepository = GetIt.instance<UserRepository>();

  Future<void> toggleFollow() async {
    final currentlyFollowing = state.isFollowing;
    final userId = state.userId;

    // Update UI immediately for better UX
    emit(
      state.copyWith(
        status: FollowManagementStatus.loading,
        isFollowing: !currentlyFollowing,
      ),
    );

    try {
      if (!currentlyFollowing) {
        // Follow user
        await _userRepository.followUser(FollowRequest(followedId: userId));
        emit(
          state.copyWith(
            status: FollowManagementStatus.success,
            isFollowing: true,
            errorMessage: null,
          ),
        );
      } else {
        // Unfollow user - Note: We need the followId for unfollow
        // For now, we'll use the userId as followId (this might need adjustment based on API)
        await _userRepository.unfollowUser(userId);
        emit(
          state.copyWith(
            status: FollowManagementStatus.success,
            isFollowing: false,
            errorMessage: null,
          ),
        );
      }
    } catch (e) {
      // Revert the UI change on error
      emit(
        state.copyWith(
          status: FollowManagementStatus.failure,
          isFollowing: currentlyFollowing,
          errorMessage: e.toString(),
        ),
      );
    }
  }

  void updateFollowStatus(bool isFollowing) {
    emit(
      state.copyWith(
        isFollowing: isFollowing,
        status: FollowManagementStatus.success,
      ),
    );
  }
}
