part of 'follow_management_cubit.dart';

enum FollowManagementStatus { initial, loading, success, failure }

extension FollowManagementStatusX on FollowManagementStatus {
  bool get isInitial => this == FollowManagementStatus.initial;
  bool get isLoading => this == FollowManagementStatus.loading;
  bool get isSuccess => this == FollowManagementStatus.success;
  bool get isFailure => this == FollowManagementStatus.failure;
}

class FollowManagementState {
  final String userId;
  final bool isFollowing;
  final FollowManagementStatus status;
  final String? errorMessage;

  const FollowManagementState({
    required this.userId,
    this.isFollowing = false,
    this.status = FollowManagementStatus.initial,
    this.errorMessage,
  });

  FollowManagementState copyWith({
    String? userId,
    bool? isFollowing,
    FollowManagementStatus? status,
    String? errorMessage,
  }) {
    return FollowManagementState(
      userId: userId ?? this.userId,
      isFollowing: isFollowing ?? this.isFollowing,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}
