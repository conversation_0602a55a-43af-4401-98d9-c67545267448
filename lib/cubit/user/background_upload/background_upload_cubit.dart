import 'dart:io';

import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:get_it/get_it.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:toii_social/core/repository/social_repository.dart';

part 'background_upload_state.dart';

class BackgroundUploadCubit extends Cubit<BackgroundUploadState> {
  BackgroundUploadCubit() : super(const BackgroundUploadState());

  final ImagePicker _imagePicker = ImagePicker();
  final ImageCropper _imageCropper = ImageCropper();
  final SocialRepository _socialRepository = GetIt.instance<SocialRepository>();

  // Callback function to be called when upload is successful
  Function(String mediaKey)? onUploadSuccess;

  /// Pick image from gallery with cropping
  Future<void> pickImageFromGallery() async {
    try {
      emit(state.copyWith(status: BackgroundUploadStatus.loading));

      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (image != null) {
        // Crop the image with background-specific settings
        final croppedFile = await _cropImage(image.path);
        if (croppedFile != null) {
          // Update local image path with cropped image
          emit(
            state.copyWith(
              localImagePath: croppedFile.path,
              errorMessage: null,
            ),
          );

          // Automatically upload the cropped image
          await uploadBackground();
        } else {
          emit(
            state.copyWith(
              status: BackgroundUploadStatus.initial,
              errorMessage: null,
            ),
          );
        }
      } else {
        emit(
          state.copyWith(
            status: BackgroundUploadStatus.initial,
            errorMessage: null,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          status: BackgroundUploadStatus.failure,
          errorMessage: 'Error selecting image: ${e.toString()}',
        ),
      );
    } finally {
      EasyLoading.dismiss();
      emit(
        state.copyWith(
          status: BackgroundUploadStatus.initial,
          errorMessage: null,
        ),
      );
    }
  }

  /// Pick image from camera with cropping
  Future<void> pickImageFromCamera() async {
    try {
      emit(state.copyWith(status: BackgroundUploadStatus.loading));

      final XFile? image = await _imagePicker.pickImage(
        source: ImageSource.camera,
        imageQuality: 80,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (image != null) {
        // Crop the image with background-specific settings
        final croppedFile = await _cropImage(image.path);
        if (croppedFile != null) {
          // Update local image path with cropped image
          emit(
            state.copyWith(
              localImagePath: croppedFile.path,
              errorMessage: null,
            ),
          );

          // Automatically upload the cropped image
          await uploadBackground();
        } else {
          emit(
            state.copyWith(
              status: BackgroundUploadStatus.initial,
              errorMessage: null,
            ),
          );
        }
      } else {
        emit(
          state.copyWith(
            status: BackgroundUploadStatus.initial,
            errorMessage: null,
          ),
        );
      }
    } catch (e) {
      emit(
        state.copyWith(
          status: BackgroundUploadStatus.failure,
          errorMessage: 'Error taking photo: ${e.toString()}',
        ),
      );
    } finally {
      EasyLoading.dismiss();
    }
  }

  /// Crop image with fixed portrait aspect ratio for mobile backgrounds
  Future<CroppedFile?> _cropImage(String imagePath) async {
    try {
      final croppedFile = await _imageCropper.cropImage(
        sourcePath: imagePath,
        aspectRatio: const CropAspectRatio(
          ratioX: 9,
          ratioY: 16,
        ), // Fix cứng tỷ lệ 9:16 cho màn hình đứng
        uiSettings: [
          AndroidUiSettings(
            toolbarColor: const Color(0xFF1A1A1A),
            toolbarWidgetColor: const Color(0xFFFFFFFF),
            backgroundColor: const Color(0xFF000000),
            activeControlsWidgetColor: const Color(0xFF007AFF),
            cropFrameColor: const Color(0xFF007AFF),
            cropGridColor: const Color(0x80FFFFFF),
            lockAspectRatio:
                true, // Lock aspect ratio - không cho phép thay đổi
            hideBottomControls:
                true, // Ẩn controls để không thay đổi được ratio
            showCropGrid: true,
          ),
          IOSUiSettings(
            doneButtonTitle: 'Done',
            cancelButtonTitle: 'Cancel',
            aspectRatioPickerButtonHidden: true, // Ẩn aspect ratio picker
            resetButtonHidden: false,
            rotateButtonsHidden: false,
            aspectRatioLockEnabled: true, // Lock aspect ratio cho iOS
            resetAspectRatioEnabled: false, // Không cho reset aspect ratio
            rotateClockwiseButtonHidden: false,
            hidesNavigationBar: false,
          ),
        ],
        compressFormat: ImageCompressFormat.jpg,
        compressQuality: 85,
        maxWidth: 1080, // Portrait width
        maxHeight: 1920, // Portrait height for 9:16 ratio (1080 * 16/9)
      );
      return croppedFile;
    } catch (e) {
      // Log error cropping background image
      return null;
    }
  }

  /// Upload background image to server
  Future<void> uploadBackground() async {
    if (state.localImagePath == null) {
      emit(
        state.copyWith(
          status: BackgroundUploadStatus.failure,
          errorMessage: 'No image selected',
        ),
      );
      return;
    }

    try {
      emit(
        state.copyWith(
          status: BackgroundUploadStatus.loading,
          uploadProgress: 0.0,
        ),
      );
      EasyLoading.show();
      final file = File(state.localImagePath!);

      // Upload using social repository
      var result = await _socialRepository.uploadMedia(file, 'image');

      // Debug logging
      print('BackgroundUpload: Upload result - key: ${result.data.key}');

      // Check if upload was successful and we have a media key
      if (result.data.key != null) {
        print('BackgroundUpload: Upload successful, emitting success state');
        emit(
          state.copyWith(
            status: BackgroundUploadStatus.success,
            mediaKey: result.data.key,
            uploadedImageUrl:
                state.localImagePath!, // Temporary until we get actual URL
            uploadProgress: 1.0,
            errorMessage: null,
          ),
        );

        // Call callback if provided
        if (onUploadSuccess != null) {
          print('BackgroundUpload: Calling onUploadSuccess callback');
          onUploadSuccess!(result.data.key!);
        }
      } else {
        print('BackgroundUpload: Upload failed - no media key received');
        emit(
          state.copyWith(
            status: BackgroundUploadStatus.failure,
            errorMessage: 'Upload failed: No media key received',
            uploadProgress: 0.0,
          ),
        );
      }
    } catch (e) {
      final errorMessage = 'Failed to upload background: ${e.toString()}';

      emit(
        state.copyWith(
          status: BackgroundUploadStatus.failure,
          errorMessage: errorMessage,
          uploadProgress: 0.0,
        ),
      );
    } finally {
      EasyLoading.dismiss();
    }
  }

  /// Clear the current background selection
  void clearBackground() {
    emit(const BackgroundUploadState());
  }

  /// Reset state to initial
  void resetState() {
    emit(const BackgroundUploadState());
  }
}
