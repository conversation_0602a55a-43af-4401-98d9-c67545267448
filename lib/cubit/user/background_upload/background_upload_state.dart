part of 'background_upload_cubit.dart';

enum BackgroundUploadStatus { initial, loading, success, failure }

extension BackgroundUploadStatusX on BackgroundUploadStatus {
  bool get isInitial => this == BackgroundUploadStatus.initial;
  bool get isLoading => this == BackgroundUploadStatus.loading;
  bool get isSuccess => this == BackgroundUploadStatus.success;
  bool get isFailure => this == BackgroundUploadStatus.failure;
}

final class BackgroundUploadState extends Equatable {
  final BackgroundUploadStatus status;
  final String? localImagePath;
  final String? mediaKey;
  final String? uploadedImageUrl;
  final double uploadProgress;
  final String? errorMessage;

  const BackgroundUploadState({
    this.status = BackgroundUploadStatus.initial,
    this.localImagePath,
    this.mediaKey,
    this.uploadedImageUrl,
    this.uploadProgress = 0.0,
    this.errorMessage,
  });

  BackgroundUploadState copyWith({
    BackgroundUploadStatus? status,
    String? localImagePath,
    String? mediaKey,
    String? uploadedImageUrl,
    double? uploadProgress,
    String? errorMessage,
  }) {
    return BackgroundUploadState(
      status: status ?? this.status,
      localImagePath: localImagePath ?? this.localImagePath,
      mediaKey: mediaKey ?? this.mediaKey,
      uploadedImageUrl: uploadedImageUrl ?? this.uploadedImageUrl,
      uploadProgress: uploadProgress ?? this.uploadProgress,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [
    status,
    localImagePath,
    mediaKey,
    uploadedImageUrl,
    uploadProgress,
    errorMessage,
  ];
}
