import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/user_repository.dart';
import 'package:toii_social/model/user/user_model.dart';
 
part 'user_stats_state.dart';

class UserStatsCubit extends Cubit<UserStatsState> {
  UserStatsCubit() : super(const UserStatsState());

  final UserRepository _userRepository = GetIt.instance<UserRepository>();

  void getUserStats(String userId) async {
    try {
      emit(state.copyWith(status: UserStatsStatus.loading));

      final result = await _userRepository.getUserStats(userId);

      emit(
        state.copyWith(
          status: UserStatsStatus.success,
          userStats: result.data,
        ),
      );
    } on DioException catch (e) {
      emit(
        state.copyWith(
          status: UserStatsStatus.failure,
          errorMessage: e.toString(),
        ),
      );
    } on Exception catch (_) {
      emit(
        state.copyWith(
          status: UserStatsStatus.failure,
          errorMessage: "Something went wrong",
        ),
      );
    }
  }

  void clearStats() {
    emit(const UserStatsState());
  }
}
