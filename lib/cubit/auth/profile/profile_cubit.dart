import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/auth_repository.dart';
import 'package:toii_social/model/user/user_model.dart';

part 'profile_state.dart';

class ProfileCubit extends Cubit<ProfileState> {
  ProfileCubit() : super(const ProfileState());

  final AuthRepository _authRepository = GetIt.instance<AuthRepository>();
  void getProfile() async {
    print('ProfileCubit: getProfile() called');
    try {
      final result = await _authRepository.getProfile();
      print(
        'ProfileCubit: getProfile() success, backgroundUrl=${result.data.backgroundUrl}',
      );
      emit(
        state.copyWith(status: ProfileStatus.success, userModel: result.data),
      );
    } on DioException catch (e) {
      print('ProfileCubit: getProfile() DioException: ${e.toString()}');
      emit(
        state.copyWith(
          status: ProfileStatus.failure,
          errorMessage: e.toString(),
        ),
      );
    } on Exception catch (_) {
      print('ProfileCubit: getProfile() Exception: Something went wrong');
      emit(
        state.copyWith(
          status: ProfileStatus.failure,
          errorMessage: "Something went wrong",
        ),
      );
    }
  }
}
