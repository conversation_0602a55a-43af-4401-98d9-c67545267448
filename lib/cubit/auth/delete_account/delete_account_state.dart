part of 'delete_account_cubit.dart';

enum DeleteAccountStatus { initial, loading, success, failure }

extension DeleteAccountStatusX on DeleteAccountStatus {
  bool get isInitial => this == DeleteAccountStatus.initial;
  bool get isLoading => this == DeleteAccountStatus.loading;
  bool get isSuccess => this == DeleteAccountStatus.success;
  bool get isFailure => this == DeleteAccountStatus.failure;
}

final class DeleteAccountState {
  final DeleteAccountStatus status;
  final String? message;
  final String? errorMessage;

  const DeleteAccountState({
    this.status = DeleteAccountStatus.initial,
    this.message,
    this.errorMessage,
  });

  DeleteAccountState copyWith({
    DeleteAccountStatus? status,
    String? message,
    String? errorMessage,
  }) {
    return DeleteAccountState(
      status: status ?? this.status,
      message: message ?? this.message,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}
