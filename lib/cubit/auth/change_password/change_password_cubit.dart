import 'package:dio/dio.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/auth_repository.dart';
import 'package:toii_social/model/auth/change_password/change_password_request_model.dart';
import 'package:toii_social/utils/utils/validator.dart';

part 'change_password_state.dart';

class ChangePasswordCubit extends Cubit<ChangePasswordState> {
  ChangePasswordCubit() : super(const ChangePasswordState());

  final AuthRepository _authRepository = GetIt.instance<AuthRepository>();

  void updateCurrentPassword(String password) {
    emit(state.copyWith(
      currentPassword: password,
      currentPasswordError: '',
    ));
    _validateForm();
  }

  void updateNewPassword(String password) {
    emit(state.copyWith(
      newPassword: password,
      newPasswordError: '',
    ));
    _validatePasswordStrength(password);
    _validateForm();
  }

  void updateConfirmPassword(String password) {
    emit(state.copyWith(
      confirmPassword: password,
      confirmPasswordError: '',
    ));
    _validateForm();
  }

  void toggleCurrentPasswordVisibility() {
    emit(state.copyWith(
      isCurrentPasswordVisible: !state.isCurrentPasswordVisible,
    ));
  }

  void toggleNewPasswordVisibility() {
    emit(state.copyWith(
      isNewPasswordVisible: !state.isNewPasswordVisible,
    ));
  }

  void toggleConfirmPasswordVisibility() {
    emit(state.copyWith(
      isConfirmPasswordVisible: !state.isConfirmPasswordVisible,
    ));
  }

  void toggleFaceId(bool isEnabled) {
    emit(state.copyWith(isEnableFaceId: isEnabled));
  }

  void _validatePasswordStrength(String password) {
    emit(state.copyWith(
      have8Characters: password.length >= 8,
      containsUppercase: password.containsUppercase,
      haveSpecialCharacter: password.containsSpecialCharacter,
    ));
  }

  void _validateForm() {
    bool isValid = true;
    String currentPasswordError = '';
    String newPasswordError = '';
    String confirmPasswordError = '';

    // Validate current password
    if (state.currentPassword.isEmpty) {
      currentPasswordError = 'Current password is required';
      isValid = false;
    }

    // Validate new password
    if (state.newPassword.isEmpty) {
      newPasswordError = 'New password is required';
      isValid = false;
    } else if (state.newPassword.length < 8) {
      newPasswordError = 'Password must be at least 8 characters';
      isValid = false;
    } else if (!state.newPassword.containsUppercase) {
      newPasswordError = 'Password must contain at least 1 uppercase character';
      isValid = false;
    } else if (!state.newPassword.containsSpecialCharacter) {
      newPasswordError = 'Password must contain at least 1 special character';
      isValid = false;
    }

    // Validate confirm password
    if (state.confirmPassword.isEmpty) {
      confirmPasswordError = 'Please confirm your password';
      isValid = false;
    } else if (state.newPassword != state.confirmPassword) {
      confirmPasswordError = 'Passwords do not match';
      isValid = false;
    }

    // Check if new password is different from current password
    if (state.currentPassword.isNotEmpty && 
        state.newPassword.isNotEmpty && 
        state.currentPassword == state.newPassword) {
      newPasswordError = 'New password must be different from current password';
      isValid = false;
    }

    emit(state.copyWith(
      currentPasswordError: currentPasswordError,
      newPasswordError: newPasswordError,
      confirmPasswordError: confirmPasswordError,
      isFormValid: isValid && 
                   state.have8Characters && 
                   state.containsUppercase && 
                   state.haveSpecialCharacter,
    ));
  }

  Future<void> changePassword() async {
    if (!state.isFormValid) return;

    try {
      emit(state.copyWith(status: ChangePasswordStatus.loading));

      final request = ChangePasswordRequestModel(
        currentPassword: state.currentPassword,
        newPassword: state.newPassword,
      );

      await _authRepository.changePassword(request);
      
      emit(state.copyWith(status: ChangePasswordStatus.success));
    } on DioException catch (e) {
      String errorMessage = 'Failed to change password';
      
      if (e.response?.statusCode == 400) {
        errorMessage = 'Current password is incorrect';
      } else if (e.response?.statusCode == 422) {
        errorMessage = 'Invalid password format';
      } else if (e.response?.data != null && e.response!.data['message'] != null) {
        errorMessage = e.response!.data['message'];
      }

      emit(state.copyWith(
        status: ChangePasswordStatus.failure,
        errorMessage: errorMessage,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: ChangePasswordStatus.failure,
        errorMessage: 'An unexpected error occurred',
      ));
    }
  }

  void clearErrors() {
    emit(state.copyWith(
      currentPasswordError: '',
      newPasswordError: '',
      confirmPasswordError: '',
      errorMessage: '',
    ));
  }

  void resetState() {
    emit(const ChangePasswordState());
  }
}
