part of 'change_password_cubit.dart';

enum ChangePasswordStatus { initial, loading, success, failure }

extension ChangePasswordStatusX on ChangePasswordStatus {
  bool get isInitial => this == ChangePasswordStatus.initial;
  bool get isLoading => this == ChangePasswordStatus.loading;
  bool get isSuccess => this == ChangePasswordStatus.success;
  bool get isFailure => this == ChangePasswordStatus.failure;
}

class ChangePasswordState extends Equatable {
  const ChangePasswordState({
    this.status = ChangePasswordStatus.initial,
    this.currentPassword = '',
    this.newPassword = '',
    this.confirmPassword = '',
    this.isCurrentPasswordVisible = false,
    this.isNewPasswordVisible = false,
    this.isConfirmPasswordVisible = false,
    this.currentPasswordError = '',
    this.newPasswordError = '',
    this.confirmPasswordError = '',
    this.have8Characters = false,
    this.containsUppercase = false,
    this.haveSpecialCharacter = false,
    this.isEnableFaceId = false,
    this.errorMessage = '',
    this.isFormValid = false,
  });

  final ChangePasswordStatus status;
  final String currentPassword;
  final String newPassword;
  final String confirmPassword;
  final bool isCurrentPasswordVisible;
  final bool isNewPasswordVisible;
  final bool isConfirmPasswordVisible;
  final String currentPasswordError;
  final String newPasswordError;
  final String confirmPasswordError;
  final bool have8Characters;
  final bool containsUppercase;
  final bool haveSpecialCharacter;
  final bool isEnableFaceId;
  final String errorMessage;
  final bool isFormValid;

  ChangePasswordState copyWith({
    ChangePasswordStatus? status,
    String? currentPassword,
    String? newPassword,
    String? confirmPassword,
    bool? isCurrentPasswordVisible,
    bool? isNewPasswordVisible,
    bool? isConfirmPasswordVisible,
    String? currentPasswordError,
    String? newPasswordError,
    String? confirmPasswordError,
    bool? have8Characters,
    bool? containsUppercase,
    bool? haveSpecialCharacter,
    bool? isEnableFaceId,
    String? errorMessage,
    bool? isFormValid,
  }) {
    return ChangePasswordState(
      status: status ?? this.status,
      currentPassword: currentPassword ?? this.currentPassword,
      newPassword: newPassword ?? this.newPassword,
      confirmPassword: confirmPassword ?? this.confirmPassword,
      isCurrentPasswordVisible: isCurrentPasswordVisible ?? this.isCurrentPasswordVisible,
      isNewPasswordVisible: isNewPasswordVisible ?? this.isNewPasswordVisible,
      isConfirmPasswordVisible: isConfirmPasswordVisible ?? this.isConfirmPasswordVisible,
      currentPasswordError: currentPasswordError ?? this.currentPasswordError,
      newPasswordError: newPasswordError ?? this.newPasswordError,
      confirmPasswordError: confirmPasswordError ?? this.confirmPasswordError,
      have8Characters: have8Characters ?? this.have8Characters,
      containsUppercase: containsUppercase ?? this.containsUppercase,
      haveSpecialCharacter: haveSpecialCharacter ?? this.haveSpecialCharacter,
      isEnableFaceId: isEnableFaceId ?? this.isEnableFaceId,
      errorMessage: errorMessage ?? this.errorMessage,
      isFormValid: isFormValid ?? this.isFormValid,
    );
  }

  @override
  List<Object> get props => [
        status,
        currentPassword,
        newPassword,
        confirmPassword,
        isCurrentPasswordVisible,
        isNewPasswordVisible,
        isConfirmPasswordVisible,
        currentPasswordError,
        newPasswordError,
        confirmPasswordError,
        have8Characters,
        containsUppercase,
        haveSpecialCharacter,
        isEnableFaceId,
        errorMessage,
        isFormValid,
      ];
}
