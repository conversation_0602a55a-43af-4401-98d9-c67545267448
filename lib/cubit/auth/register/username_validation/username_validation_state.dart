part of 'username_validation_cubit.dart';

enum UsernameValidationStatus { initial, loading, success, failure }

extension UsernameValidationStatusX on UsernameValidationStatus {
  bool get isInitial => this == UsernameValidationStatus.initial;
  bool get isLoading => this == UsernameValidationStatus.loading;
  bool get isSuccess => this == UsernameValidationStatus.success;
  bool get isFailure => this == UsernameValidationStatus.failure;
}

final class UsernameValidationState {
  final UsernameValidationStatus status;
  final bool isAvailable;
  final String? errorMessage;
  final String? username;

  const UsernameValidationState({
    this.status = UsernameValidationStatus.initial,
    this.isAvailable = false,
    this.errorMessage,
    this.username,
  });

  UsernameValidationState copyWith({
    UsernameValidationStatus? status,
    bool? isAvailable,
    String? errorMessage,
    String? username,
  }) {
    return UsernameValidationState(
      status: status ?? this.status,
      isAvailable: isAvailable ?? this.isAvailable,
      errorMessage: errorMessage ?? this.errorMessage,
      username: username ?? this.username,
    );
  }
}
