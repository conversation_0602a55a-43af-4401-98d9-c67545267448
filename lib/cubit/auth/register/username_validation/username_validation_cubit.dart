import 'dart:async';

import 'package:dio/dio.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/core/repository/auth_repository.dart';

part 'username_validation_state.dart';

class UsernameValidationCubit extends Cubit<UsernameValidationState> {
  UsernameValidationCubit() : super(const UsernameValidationState());

  final AuthRepository _authRepository = GetIt.instance<AuthRepository>();
  Timer? _debounceTimer;

  void validateUsername(String username) {
    // Cancel previous timer if exists
    _debounceTimer?.cancel();

    // Clear previous state if username is empty
    if (username.trim().isEmpty) {
      emit(state.copyWith(
        status: UsernameValidationStatus.initial,
        isAvailable: false,
        errorMessage: null,
      ));
      return;
    }

    // Set loading state immediately
    emit(state.copyWith(
      status: UsernameValidationStatus.loading,
      isAvailable: false,
      errorMessage: null,
    ));

    // Debounce API call by 500ms
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      _checkUsernameAvailability(username.trim());
    });
  }

  Future<void> _checkUsernameAvailability(String username) async {
    try {
      final result = await _authRepository.checkUsernameAvailability(username);
      
      emit(state.copyWith(
        status: UsernameValidationStatus.success,
        isAvailable: result.data.available,
        errorMessage: result.data.available ? null : result.data.message,
        username: username,
      ));
    } on DioException catch (e) {
      String errorMessage = 'Failed to check username availability';
      
      if (e.response?.statusCode == 400) {
        errorMessage = 'Invalid username format';
      } else if (e.response?.statusCode == 429) {
        errorMessage = 'Too many requests. Please try again later';
      } else if (e.response?.statusCode == 500) {
        errorMessage = 'Server error. Please try again later';
      }

      emit(state.copyWith(
        status: UsernameValidationStatus.failure,
        isAvailable: false,
        errorMessage: errorMessage,
      ));
    } catch (e) {
      emit(state.copyWith(
        status: UsernameValidationStatus.failure,
        isAvailable: false,
        errorMessage: 'Network error. Please check your connection',
      ));
    }
  }

  void clearValidation() {
    _debounceTimer?.cancel();
    emit(const UsernameValidationState());
  }

  @override
  Future<void> close() {
    _debounceTimer?.cancel();
    return super.close();
  }
}
