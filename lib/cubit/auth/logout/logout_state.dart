part of 'logout_cubit.dart';

enum LogoutStatus { initial, loading, success, failure }

extension LogoutStatusX on LogoutStatus {
  bool get isInitial => this == LogoutStatus.initial;
  bool get isLoading => this == LogoutStatus.loading;
  bool get isSuccess => this == LogoutStatus.success;
  bool get isFailure => this == LogoutStatus.failure;
}

final class LogoutState {
  final LogoutStatus status;
  final String? errorMessage;

  const LogoutState({
    this.status = LogoutStatus.initial,
    this.errorMessage,
  });

  LogoutState copyWith({
    LogoutStatus? status,
    String? errorMessage,
  }) {
    return LogoutState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}
