import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_social/cubit/wallet/crypto/crypto_default_manager.dart';
import 'package:toii_social/cubit/wallet/crypto/saved_crypto.dart';
import 'package:toii_social/model/crypto/crypto.dart';

part 'import_token_state.dart';

class ImportTokenCubit extends Cubit<ImportTokenState> {
  ImportTokenCubit({required this.walletAddress})
    : super(const ImportTokenState());
  final String walletAddress;
  final cryptoDefaultManager = CryptoDefaultManager();

  void getData() async {
    final lists = await cryptoDefaultManager.getDefaultTokens();


    emit(state.copyWith(tokens: lists));
  }

  
}
