part of 'manage_token_cubit.dart';


class ManageTokenState extends Equatable {
  final List<Crypto> tokens;
  final List<Crypto> tokensOn;
  const ManageTokenState({ this.tokens = const [], this.tokensOn = const [] });
  @override
  List<Object?> get props => [ tokens, tokensOn ];

  ManageTokenState copyWith({
  List<Crypto>? tokens,
  List<Crypto>? tokensOn,
  }) {
    return ManageTokenState(
      tokens: tokens ?? this.tokens,
      tokensOn: tokensOn ?? this.tokensOn,
    );
  }
}
