import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:toii_social/cubit/wallet/crypto/crypto_default_manager.dart';
import 'package:toii_social/cubit/wallet/crypto/saved_crypto.dart';
import 'package:toii_social/model/crypto/crypto.dart';

part 'manage_token_state.dart';

class ManageTokenCubit extends Cubit<ManageTokenState> {
  ManageTokenCubit({required this.walletAddress})
    : super(const ManageTokenState());
  final String walletAddress;
  final cryptoDefaultManager = CryptoDefaultManager();

  void getData() async {
    final lists = await cryptoDefaultManager.getDefaultTokens();

    final listsOn = await SavedCrypto.instance.getSavedCrypto(walletAddress);

    emit(state.copyWith(tokens: lists, tokensOn: listsOn));
  }

  void toggleCanDisplay(Crypto token) async {
    List<Crypto> savedCryptos = await SavedCrypto.instance.getSavedCrypto(
      walletAddress,
    );
    final targetCrypto =
        savedCryptos
            .where(
              (e) => e.chainId == token.chainId && e.symbol == token.symbol,
            )
            .firstOrNull;

    if (targetCrypto == null) {
      savedCryptos.add(token);
    } else {
      final targetIndex = savedCryptos.indexWhere(
        (e) => e.chainId == token.chainId && e.symbol == token.symbol,
      );
      if (targetIndex < 0) {
        throw Exception("Invalid Index");
      }
      savedCryptos.removeAt(targetIndex);
    }

  await  SavedCrypto.instance.saveListCrypto(savedCryptos, walletAddress);
   emit(state.copyWith(tokensOn: savedCryptos));
  }

  void addNetwork(Crypto crypto) async {
    List<Crypto> savedCryptos = await SavedCrypto.instance.getSavedCrypto(
      walletAddress,
    );
    savedCryptos.add(crypto);
    await SavedCrypto.instance.saveListCrypto(savedCryptos, walletAddress);
    emit(state.copyWith(tokens: savedCryptos));
  }
}
