import 'package:hive_ce/hive.dart';
import 'package:toii_social/logger/logger.dart';

class WalletKeys {
  final boxName = "userMultipleWallets";
}

class WalletDatabase {
  // final _secureService = SecureStorageService();
  // final _encryptService = EncryptService();
  // final _prefs = PublicDataManager();
  // final _addressManager = AddressManager();
  final _keys = WalletKeys();
  // final cryptoRequestManager = CryptoRequestManager();

  Future<Box?> getBox() async {
    try {
      await Hive.openBox(_keys.boxName);
      return Hive.box(_keys.boxName);
    } catch (e) {
      logError(e.toString());
      return null;
    }
  }

  Future<bool> saveDynamicData({
    required dynamic data,
    required String boxName,
  }) async {
    try {
      final box = await getBox();
      if (box == null) {
        throw "Box Not Initialized";
      }
      await box.put(boxName, data);
      return true;
    } catch (e) {
      logError(e.toString());
      return false;
    }
  }

  Future<dynamic> getDynamicData({required String name}) async {
    try {
      final box = await getBox();
      if (box == null) {
        throw "Box Not Initialized";
      }
      final savedWallets = box.get(name);
      if (savedWallets != null) {
        return savedWallets;
      }
      return null;
    } catch (e) {
      logError(e.toString());
      return null;
    }
  }
}
