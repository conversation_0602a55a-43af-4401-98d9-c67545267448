import 'package:toii_social/cubit/wallet/crypto/crypto_storage_manager.dart';
import 'package:toii_social/logger/logger.dart';
import 'package:toii_social/model/crypto/crypto.dart';

import 'crypto_default_manager.dart';

class SavedCrypto {
  final cryptoStorage = CryptoStorageManager();
  final cryptoDefaultManager = CryptoDefaultManager();
  SavedCrypto._privateConstructor();

  static final SavedCrypto instance = SavedCrypto._privateConstructor();

  Future<List<Crypto>> getSavedCrypto(String address) async {
    try {
      if (address.isEmpty) {
        logError("No active account");
        return [];
      }

      List<Crypto> savedCryptos = [];
      List<Crypto> standardCrypto = [];

      try {
        savedCryptos = await cryptoStorage.getSavedCryptos(address: address);
      } catch (e) {
        logError(e.toString());
      }

      if (savedCryptos.isNotEmpty) {
        return savedCryptos;
      }

      try {
        final defaultTokens = await cryptoDefaultManager.getDefaultTokens();

        standardCrypto =
            defaultTokens
                .where((crypto) {
                  final chainId = crypto.chainId;

                  return listFav.contains(chainId);
                })
                .map((e) => e.copyWith(canDisplay: true))
                .toList();
      } catch (e) {
        logError(e.toString());
      }

      if (standardCrypto.isNotEmpty) {
        await saveListCrypto(standardCrypto, address);
        return standardCrypto;
      }
      return [];
    } catch (e) {
      logError(e.toString());
      return [];
    }
  }

  Future<bool> saveListCrypto(List<Crypto> cryptos, String address) async {
    try {
      final result = await cryptoStorage.saveListCrypto(
        cryptos: cryptos,
        address: address,
      );
      return result;
    } catch (e) {
      logError(e.toString());
      return false;
    }
  }
}
