import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:toii_social/model/crypto/crypto.dart';

const List<int> topChainIds = [
  472025,
  1,
  56,
  137,
  10,
  42161,
  43114,
  8453,
  250,
  324,
  1101,
  59144,
  5000,
  204,
  100,
  1088,
  1284,
  42220,
  9001,
  888,
  81457,
];

const List<int> listFav = [
 472025,
  1,
  56,
  137,
  8453,
];

class CryptoDefaultManager {
  Future<List<Crypto>> getDefaultTokens() async {
    final String response = await rootBundle.loadString(
      'assets/json/chainlist.json',
    );

    final List<dynamic> data = jsonDecode(response);

    final filtered =
        data
            .where((json) {
              final chainId = json['chainId'] as int?;

              return topChainIds.contains(chainId);
            })
            .map((json) => Crypto.fromJson(json))
            .toList();
    return filtered;
  }
}
