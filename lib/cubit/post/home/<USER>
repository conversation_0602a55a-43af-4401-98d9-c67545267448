part of 'home_cubit.dart';

enum HomeStatus { initial, loading, success, failure, noMoreData }

enum FollowingStatus { initial, loading, success, failure, noMoreData }

extension HomeX on HomeStatus {
  bool get isInitial => this == HomeStatus.initial;
  bool get isLoading => this == HomeStatus.loading;
  bool get isSuccess => this == HomeStatus.success;
  bool get isFailure => this == HomeStatus.failure;
}

extension FollowingX on FollowingStatus {
  bool get isInitial => this == FollowingStatus.initial;
  bool get isLoading => this == FollowingStatus.loading;
  bool get isSuccess => this == FollowingStatus.success;
  bool get isFailure => this == FollowingStatus.failure;
}

final class HomeState {
  final List<PostModel> posts;
  final HomeStatus status;
  final String? errorMessage;
  final bool canLoadMore;

  // Following feed properties
  final List<PostModel> followingPosts;
  final FollowingStatus followingStatus;
  final String? followingErrorMessage;
  final bool canLoadMoreFollowing;

  const HomeState({
    this.posts = const [],
    this.status = HomeStatus.initial,
    this.errorMessage,
    this.canLoadMore = true,
    this.followingPosts = const [],
    this.followingStatus = FollowingStatus.initial,
    this.followingErrorMessage,
    this.canLoadMoreFollowing = true,
  });

  HomeState copyWith({
    List<PostModel>? posts,
    HomeStatus? status,
    String? errorMessage,
    bool? canLoadMore,
    List<PostModel>? followingPosts,
    FollowingStatus? followingStatus,
    String? followingErrorMessage,
    bool? canLoadMoreFollowing,
  }) {
    return HomeState(
      posts: posts ?? this.posts,
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      canLoadMore: canLoadMore ?? this.canLoadMore,
      followingPosts: followingPosts ?? this.followingPosts,
      followingStatus: followingStatus ?? this.followingStatus,
      followingErrorMessage:
          followingErrorMessage ?? this.followingErrorMessage,
      canLoadMoreFollowing: canLoadMoreFollowing ?? this.canLoadMoreFollowing,
    );
  }
}
