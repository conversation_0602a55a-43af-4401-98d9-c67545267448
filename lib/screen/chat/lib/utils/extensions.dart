import 'package:nearby_service/nearby_service.dart';

extension ChannalPreviewName on CommunicationChannelState {
  String get previewName {
    return switch (this) {
      CommunicationChannelState.notConnected => 'Not connected',
      CommunicationChannelState.loading => 'Connecting',
      CommunicationChannelState.connected => 'Connected',
    };
  }
}
extension StringExtension on String {
    String capitalize() {
      return "${this[0].toUpperCase()}${this.substring(1).toLowerCase()}";
    }
}