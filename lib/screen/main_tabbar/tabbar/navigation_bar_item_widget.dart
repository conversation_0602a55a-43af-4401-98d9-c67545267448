import 'package:flutter/material.dart';

/// A tab to display in a [CrystalNavigationBar]
class NavigationBarItemWidget {
  /// An icon to display.
  final Widget icon;

  /// An icon to display.
  final Widget? unselectedIcon;

  ///badge
  final Badge? badge;

  /// A primary color to use for this tab.
  final Color? selectedColor;

  /// The color to display when this tab is not selected.
  final Color? unselectedColor;

  NavigationBarItemWidget({
    required this.icon,
    this.unselectedIcon,
    this.selectedColor,
    this.unselectedColor,
    this.badge,
  });
}