import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/screen/main_tabbar/tabbar/navigation_bar_item_widget.dart';
import 'package:toii_social/screen/main_tabbar/tabbar/tabbar_body_widget.dart';
import 'package:toii_social/widget/colors/colors.dart';

BottomAppBar tabbarHome({
  required int currentIndex,
  required Function(int index) onTap,
}) {
  return BottomAppBar(
    color: Colors.transparent,
    child: BottomAppBar(
      color: Colors.transparent,
      padding: EdgeInsets.zero,
      elevation: 0,
      height: 64,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Container(
          decoration: BoxDecoration(
            boxShadow: const [
              BoxShadow(
                color: Colors.transparent,
                spreadRadius: 0,
                blurRadius: 0,
                offset: Offset(0, 0), // changes position of shadow
              ),
            ], // Apply the shadow here
            borderRadius: BorderRadius.circular(30),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(30),
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaY: 10, sigmaX: 10),
              child: Container(
                padding: const EdgeInsets.only(bottom: 5, top: 5),
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(30),
                  border: Border.all(width: 0.0, color: Colors.white24),
                  color: Colors.black.withValues(alpha: 0.5),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: TabbarBodyWidget(
                    items: [
                      /// Home
                      NavigationBarItemWidget(
                        icon: SvgPicture.asset(
                          Assets.icons.icTabbarHomeActive.path,
                        ),
                        unselectedIcon: SvgPicture.asset(
                          Assets.icons.icTabbarHome.path,
                        ),
                        selectedColor: themeData.primaryGreen500,
                        unselectedColor: themeData.white900,
                        // badge: Badge(
                        //   label: Text(
                        //     "9+",
                        //     style: TextStyle(color: Colors.white),
                        //   ),
                        // ),
                      ),
                      NavigationBarItemWidget(
                        icon: SvgPicture.asset(
                          Assets.icons.icTabbarExplore.path,
                          colorFilter: ColorFilter.mode(
                            themeData.primaryGreen500,
                            BlendMode.srcIn,
                          ),
                        ),
                        unselectedIcon: SvgPicture.asset(
                          Assets.icons.icTabbarExplore.path,
                          colorFilter: ColorFilter.mode(
                            themeData.white900,
                            BlendMode.srcIn,
                          ),
                        ),
                        selectedColor: themeData.primaryGreen500,
                        unselectedColor: themeData.white900,
                      ),

                      /// Add
                      NavigationBarItemWidget(
                        icon: SvgPicture.asset(
                          Assets.icons.icTabbarAdd.path,
                          // colorFilter: ColorFilter.mode(
                          //   themeData.primaryGreen500,
                          //   BlendMode.srcIn,
                          // ),
                        ),
                        unselectedIcon: SvgPicture.asset(
                          Assets.icons.icTabbarAdd.path,
                          // colorFilter: ColorFilter.mode(
                          //   themeData.white900,
                          //   BlendMode.srcIn,
                          // ),
                        ),
                        selectedColor: themeData.primaryGreen500,
                        unselectedColor: themeData.white900,
                      ),
                      NavigationBarItemWidget(
                        icon: SvgPicture.asset(
                          Assets.icons.icTabbarWallet.path,
                          colorFilter: ColorFilter.mode(
                            themeData.primaryGreen500,
                            BlendMode.srcIn,
                          ),
                        ),
                        unselectedIcon: SvgPicture.asset(
                          Assets.icons.icTabbarWallet.path,
                          colorFilter: ColorFilter.mode(
                            themeData.white900,
                            BlendMode.srcIn,
                          ),
                        ),
                        selectedColor: themeData.primaryGreen500,
                        unselectedColor: themeData.white900,
                      ),

                      /// Profile
                      NavigationBarItemWidget(
                        icon: SvgPicture.asset(
                          Assets.icons.icTabbarProfile.path,
                          colorFilter: ColorFilter.mode(
                            themeData.primaryGreen500,
                            BlendMode.srcIn,
                          ),
                        ),
                        unselectedIcon: SvgPicture.asset(
                          Assets.icons.icTabbarProfile.path,
                          colorFilter: ColorFilter.mode(
                            themeData.white900,
                            BlendMode.srcIn,
                          ),
                        ),
                        selectedColor: themeData.primaryGreen500,
                        unselectedColor: themeData.white900,
                      ),
                    ],
                    currentIndex: currentIndex,
                    curve: Curves.easeOutQuint,
                    duration: const Duration(milliseconds: 500),
                    selectedItemColor: themeData.primaryGreen500,
                    theme: themeData,
                    unselectedItemColor: themeData.white900,
                    onTap: onTap,
                    itemPadding: const EdgeInsets.symmetric(
                      vertical: 4,
                      horizontal: 10,
                    ),
                    indicatorColor: themeData.primaryGreen500,
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    ),
  );
}
