import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/screen/user_profile/setting_profile/theme_selection/theme_option_item.dart';
import 'package:toii_social/widget/app_bar/app_bar.dart';
import 'package:toii_social/widget/colors/colors.dart';

class ThemeSelectionScreen extends StatefulWidget {
  const ThemeSelectionScreen({super.key});

  @override
  State<ThemeSelectionScreen> createState() => _ThemeSelectionScreenState();
}

class _ThemeSelectionScreenState extends State<ThemeSelectionScreen> {
  late ThemeCubit _themeCubit;

  @override
  void initState() {
    super.initState();
    _themeCubit = GetIt.instance<ThemeCubit>();
  }

  @override
  Widget build(BuildContext context) {
    final themeData = Theme.of(context);

    return Scaffold(
      backgroundColor: themeData.neutral50,
      appBar: const BaseAppBar(title: 'Theme', centerTitle: false),
      body: BlocProvider.value(
        value: _themeCubit,
        child: BlocBuilder<ThemeCubit, ThemeState>(
          builder: (context, state) {
            return Container(
              margin: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: themeData.neutral50,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  ThemeOptionItem(
                    title: 'Light',
                    value: AppTheme.light,
                    selectedValue: _themeCubit.currentTheme,
                    onChanged: (theme) {
                      // _themeCubit.toggleTheme(theme);
                    },
                  ),
                  ThemeOptionItem(
                    title: 'Dark',
                    value: AppTheme.dark,
                    selectedValue: _themeCubit.currentTheme,
                    onChanged: (theme) {
                      // _themeCubit.toggleTheme(theme);
                    },
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
