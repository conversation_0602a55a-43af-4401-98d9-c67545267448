import 'package:flutter/material.dart';
import 'package:toii_social/screen/user_profile/setting_profile/language_selection/language_option_item.dart';
import 'package:toii_social/widget/app_bar/app_bar.dart';
import 'package:toii_social/widget/colors/colors.dart';

class LanguageSelectionScreen extends StatefulWidget {
  const LanguageSelectionScreen({super.key});

  @override
  State<LanguageSelectionScreen> createState() => _LanguageSelectionScreenState();
}

class _LanguageSelectionScreenState extends State<LanguageSelectionScreen> {
  // Since we only display English and no backend logic is needed,
  // we'll just show English as selected
  String selectedLanguage = 'English';

  @override
  Widget build(BuildContext context) {
    final themeData = Theme.of(context);

    return Scaffold(
      backgroundColor: themeData.neutral50,
      appBar: const BaseAppBar(
        title: 'Language',
        centerTitle: false,
      ),
      body: Container(
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: themeData.neutral50,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            LanguageOptionItem(
              title: 'English',
              isSelected: selectedLanguage == 'English',
              onTap: () {
                // No actual language switching logic needed as per requirements
                // Just update the UI state for visual feedback
                setState(() {
                  selectedLanguage = 'English';
                });
              },
            ),
          ],
        ),
      ),
    );
  }
}
