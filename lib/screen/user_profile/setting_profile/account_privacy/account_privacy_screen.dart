import 'package:flutter/material.dart';
import 'package:toii_social/screen/user_profile/setting_profile/account_privacy/privacy_setting_item.dart';
import 'package:toii_social/widget/app_bar/app_bar.dart';
import 'package:toii_social/widget/colors/colors.dart';

class AccountPrivacyScreen extends StatefulWidget {
  const AccountPrivacyScreen({super.key});

  @override
  State<AccountPrivacyScreen> createState() => _AccountPrivacyScreenState();
}

class _AccountPrivacyScreenState extends State<AccountPrivacyScreen> {
  // Since we only need UI and no backend logic is required,
  // we'll just manage the toggle state locally
  bool manuallyApproveFollowers = true;

  @override
  Widget build(BuildContext context) {
    final themeData = Theme.of(context);

    return Scaffold(
      backgroundColor: themeData.neutral50,
      appBar: const BaseAppBar(
        title: 'Account privacy',
        centerTitle: false,
      ),
      body: Container(
        margin: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: themeData.neutral50,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            PrivacySettingItem(
              title: 'Manually approve followers',
              description: 'Enable to approve follow requests manually',
              value: manuallyApproveFollowers,
              onChanged: (value) {
                // No actual privacy logic needed as per requirements
                // Just update the UI state for visual feedback
                setState(() {
                  manuallyApproveFollowers = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }
}
