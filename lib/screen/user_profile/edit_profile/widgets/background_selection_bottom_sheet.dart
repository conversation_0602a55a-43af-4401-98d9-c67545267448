import 'package:flutter/material.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/widget/bottom_sheet/tt_bottom_sheet.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class BackgroundSelectionBottomSheet extends StatelessWidget {
  final VoidCallback onGalleryTap;
  final VoidCallback onCameraTap;
  final VoidCallback onAIGenerateTap;

  const BackgroundSelectionBottomSheet({
    super.key,
    required this.onGalleryTap,
    required this.onCameraTap,
    required this.onAIGenerateTap,
  });

  @override
  Widget build(BuildContext context) {
    final themeData = Theme.of(context);
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Upload from Gallery option
        _buildBackgroundOption(
          context: context,
          icon: Assets.icons.icPicture.svg(
            width: 24,
            height: 24,
            colorFilter: ColorFilter.mode(
              themeData.textSecondary,
              BlendMode.srcIn,
            ),
          ),
          title: 'Upload',
          onTap: () {
            Navigator.pop(context);
            onGalleryTap();
          },
        ),

        // Take Photo option
        _buildBackgroundOption(
          context: context,
          icon: Icon(
            Icons.camera_alt,
            size: 24,
            color: themeData.textSecondary,
          ),
          title: 'Take Photo',
          onTap: () {
            Navigator.pop(context);
            onCameraTap();
          },
        ),

        // Generate with AI option (Coming soon)
        _buildBackgroundOption(
          context: context,
          icon: Assets.icons.icGenAi.svg(
            width: 24,
            height: 24,
            colorFilter: ColorFilter.mode(
              themeData.textSecondary,
              BlendMode.srcIn,
            ),
          ),
          title: 'Generate with AI',
          onTap: () {
            Navigator.pop(context);
            onAIGenerateTap();
          },
        ),
      ],
    );
  }

  Widget _buildBackgroundOption({
    required BuildContext context,
    required Widget icon,
    required String title,
    required VoidCallback onTap,
  }) {
    final themeData = Theme.of(context);
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Row(
          children: [
            icon,
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: titleMedium.copyWith(
                  color: themeData.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Helper function to show the background selection bottom sheet
  static Future<void> show({
    required BuildContext context,
    required VoidCallback onGalleryTap,
    required VoidCallback onCameraTap,
    required VoidCallback onAIGenerateTap,
  }) {
    return showTtBottomSheet(
      context,
      child: BackgroundSelectionBottomSheet(
        onGalleryTap: onGalleryTap,
        onCameraTap: onCameraTap,
        onAIGenerateTap: onAIGenerateTap,
      ),
    );
  }
}
