import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/auth/delete_account/delete_account_cubit.dart';
import 'package:toii_social/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';

class DeleteAccountDialog extends StatefulWidget {
  final VoidCallback? onDeleteConfirmed;
  final VoidCallback? onCancel;

  const DeleteAccountDialog({super.key, this.onDeleteConfirmed, this.onCancel});

  @override
  State<DeleteAccountDialog> createState() => _DeleteAccountDialogState();
}

class _DeleteAccountDialogState extends State<DeleteAccountDialog> {
  late final DeleteAccountCubit _deleteAccountCubit;
  late final ProfileCubit _profileCubit;

  @override
  void initState() {
    super.initState();
    _deleteAccountCubit = GetIt.instance<DeleteAccountCubit>();
    _profileCubit = GetIt.instance<ProfileCubit>();
  }

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;

    return BlocProvider.value(
      value: _deleteAccountCubit,
      child: BlocConsumer<DeleteAccountCubit, DeleteAccountState>(
        listener: (context, state) {
          if (state.status.isSuccess) {
            Navigator.of(context).pop();
            context.showSnackbar(
              message: state.message ?? "Account deleted successfully",
            );
            // Navigation to login screen will be handled by LogoutCubit
            context.go(RouterEnums.inital.routeName);
          } else if (state.status.isFailure) {
            Navigator.of(context).pop();
            context.showSnackbar(
              message: state.errorMessage ?? "Failed to delete account",
            );
          }
        },
        builder: (context, state) {
          return Dialog(
            backgroundColor: Colors.transparent,
            child: Container(
              width: 328,
              decoration: BoxDecoration(
                color: themeData.neutral50,
                borderRadius: BorderRadius.circular(28),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.16),
                    blurRadius: 36,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Warning Icon Section
                  Container(
                    padding: const EdgeInsets.only(top: 20, bottom: 8),
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: themeData.neutral50,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Assets.icons.icCloseCircle.svg(
                        width: 33.33,
                        height: 33.33,
                        colorFilter: ColorFilter.mode(
                          themeData.red500,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ),

                  // Title Section
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 16,
                    ),
                    child: Text(
                      'Are you sure you want to delete your account?',
                      style: titleMedium.copyWith(
                        fontWeight: FontWeight.w700,
                        fontSize: 19,
                        color: themeData.textPrimary,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                  // Description Section
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 8,
                    ),
                    child: Text(
                      'Deleting your account is permanent. All data, posts, and collected NFTs will be removed, and you\'ll lose your username and connections.',
                      style: bodyMedium.copyWith(
                        fontSize: 16,
                        color: themeData.textPrimary,
                        height: 1.5,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),

                  // Action Buttons Section
                  Container(
                    padding: const EdgeInsets.fromLTRB(24, 16, 24, 24),
                    child: Row(
                      children: [
                        // Cancel Button
                        Expanded(
                          child: Container(
                            height: 48,
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.8),
                              borderRadius: BorderRadius.circular(48),
                            ),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                onTap:
                                    state.status.isLoading
                                        ? null
                                        : () {
                                          Navigator.of(context).pop();
                                          widget.onCancel?.call();
                                        },
                                borderRadius: BorderRadius.circular(48),
                                child: Center(
                                  child: Text(
                                    'Cancel',
                                    style: titleMedium.copyWith(
                                      fontWeight: FontWeight.w600,
                                      fontSize: 16,
                                      color: Colors.white.withOpacity(0.9),
                                      letterSpacing: 0.01,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),

                        const SizedBox(width: 8),

                        // Delete Button
                        Expanded(
                          child: Container(
                            height: 48,
                            decoration: BoxDecoration(
                              color: themeData.red500,
                              borderRadius: BorderRadius.circular(48),
                            ),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                onTap:
                                    state.status.isLoading
                                        ? null
                                        : () {
                                          // Get current user ID from ProfileCubit
                                          final currentUser =
                                              _profileCubit.state.userModel;
                                          if (currentUser != null) {
                                            _deleteAccountCubit.deleteAccount(
                                              currentUser.id,
                                            );
                                          } else {
                                            context.showSnackbar(
                                              message:
                                                  "Unable to get user information",
                                            );
                                            Navigator.of(context).pop();
                                          }
                                        },
                                borderRadius: BorderRadius.circular(48),
                                child: Center(
                                  child:
                                      state.status.isLoading
                                          ? SizedBox(
                                            width: 16,
                                            height: 16,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              valueColor:
                                                  AlwaysStoppedAnimation<Color>(
                                                    Colors.white,
                                                  ),
                                            ),
                                          )
                                          : Text(
                                            'Delete',
                                            style: titleMedium.copyWith(
                                              fontWeight: FontWeight.w600,
                                              fontSize: 16,
                                              color: Colors.white.withOpacity(
                                                0.9,
                                              ),
                                              letterSpacing: 0.01,
                                            ),
                                          ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}
