import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/user_profile/account_info/widget/account_info_list_item.dart';
import 'package:toii_social/screen/user_profile/account_info/widget/delete_account_dialog.dart';
import 'package:toii_social/widget/app_bar/app_bar.dart';
import 'package:toii_social/widget/colors/colors.dart';

class AccountInfoScreen extends StatefulWidget {
  const AccountInfoScreen({super.key});

  @override
  State<AccountInfoScreen> createState() => _AccountInfoScreenState();
}

class _AccountInfoScreenState extends State<AccountInfoScreen> {
  late ProfileCubit _profileCubit;

  @override
  void initState() {
    super.initState();
    _profileCubit = GetIt.instance<ProfileCubit>();

    // Load profile data if not already loaded
    if (_profileCubit.state.status.isInitial) {
      _profileCubit.getProfile();
    }
  }

  String _getDisplayValue(String? value) {
    return value?.isNotEmpty == true ? value! : "-";
  }

  String _getBirthdayDisplay(Map<String, dynamic>? moreInfo) {
    if (moreInfo != null && moreInfo.containsKey('birthday')) {
      final birthday = moreInfo['birthday'];
      if (birthday != null && birthday.toString().isNotEmpty) {
        return birthday.toString();
      }
    }
    return "Update";
  }

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;

    return Scaffold(
      backgroundColor: themeData.neutral50,
      appBar: const BaseAppBar(title: 'Account', centerTitle: false),
      body: Column(
        children: [
          // Account Information List
          Expanded(
            child: BlocProvider.value(
              value: _profileCubit,
              child: BlocBuilder<ProfileCubit, ProfileState>(
                builder: (context, state) {
                  if (state.status.isLoading) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (state.status.isFailure) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 48,
                            color: themeData.red500,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Failed to load account information',
                            style: TextStyle(color: themeData.red500),
                          ),
                          const SizedBox(height: 8),
                          ElevatedButton(
                            onPressed: () => _profileCubit.getProfile(),
                            child: const Text('Retry'),
                          ),
                        ],
                      ),
                    );
                  }

                  final user = state.userModel;

                  return Column(
                    children: [
                      AccountInfoListItem(
                        icon: Assets.icons.icProfile.svg(
                          colorFilter: ColorFilter.mode(
                            themeData.textSecondary,
                            BlendMode.srcIn,
                          ),
                        ),
                        title: 'Username',
                        subtitle: _getDisplayValue(user?.username),
                        onTap: () {
                          // TODO: Navigate to username edit
                        },
                      ),
                      AccountInfoListItem(
                        icon: Assets.icons.icSms.svg(
                          colorFilter: ColorFilter.mode(
                            themeData.textSecondary,
                            BlendMode.srcIn,
                          ),
                        ),
                        title: 'Email',
                        subtitle: _getDisplayValue(user?.email),
                        showArrow: false,
                        onTap: () {
                          // TODO: Navigate to email edit
                        },
                      ),
                      AccountInfoListItem(
                        icon: Assets.icons.icCalendar.svg(
                          colorFilter: ColorFilter.mode(
                            themeData.textSecondary,
                            BlendMode.srcIn,
                          ),
                        ),
                        title: 'Birthday',
                        subtitle: "-",
                        onTap: () {
                          // TODO: Navigate to birthday edit
                        },
                      ),
                      AccountInfoListItem(
                        icon: Assets.icons.icLock2Svg_.svg(
                          colorFilter: ColorFilter.mode(
                            themeData.textSecondary,
                            BlendMode.srcIn,
                          ),
                        ),
                        title: 'Change password',
                        onTap: () {
                          context.push(RouterEnums.changePassword.routeName);
                        },
                      ),
                      AccountInfoListItem(
                        icon: Assets.icons.icLocation.svg(
                          colorFilter: ColorFilter.mode(
                            themeData.textSecondary,
                            BlendMode.srcIn,
                          ),
                        ),
                        title: 'Location',
                        subtitle: _getDisplayValue(user?.address),
                        onTap: () {
                          // TODO: Navigate to location edit
                        },
                      ),
                      AccountInfoListItem(
                        icon: Assets.icons.icCloseCircle.svg(
                          colorFilter: ColorFilter.mode(
                            themeData.red500,
                            BlendMode.srcIn,
                          ),
                        ),
                        title: 'Delete account',
                        titleColor: themeData.red500,
                        onTap: () {
                          showDialog(
                            context: context,
                            builder:
                                (context) => DeleteAccountDialog(
                                  onDeleteConfirmed: () {
                                    context.pop();
                                  },
                                  onCancel: () {
                                    context.pop();
                                  },
                                ),
                          );
                        },
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
