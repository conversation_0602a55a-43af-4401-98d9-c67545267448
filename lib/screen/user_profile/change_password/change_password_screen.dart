import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/auth/change_password/change_password_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/widget/app_bar/app_bar.dart';
import 'package:toii_social/widget/button/button.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/password_validation/password_validation_list.dart';
import 'package:toii_social/widget/text_field.dart/text_field.dart';

class ChangePasswordScreen extends StatefulWidget {
  const ChangePasswordScreen({super.key});

  @override
  State<ChangePasswordScreen> createState() => _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends State<ChangePasswordScreen> {
  late ChangePasswordCubit _changePasswordCubit;
  late TextEditingController _currentPasswordController;
  late TextEditingController _newPasswordController;
  late TextEditingController _confirmPasswordController;
  late FocusNode _currentPasswordFocus;
  late FocusNode _newPasswordFocus;
  late FocusNode _confirmPasswordFocus;

  @override
  void initState() {
    super.initState();
    _changePasswordCubit =  ChangePasswordCubit();
    _currentPasswordController = TextEditingController();
    _newPasswordController = TextEditingController();
    _confirmPasswordController = TextEditingController();
    _currentPasswordFocus = FocusNode();
    _newPasswordFocus = FocusNode();
    _confirmPasswordFocus = FocusNode();
  }

  @override
  void dispose() {
    _currentPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    _currentPasswordFocus.dispose();
    _newPasswordFocus.dispose();
    _confirmPasswordFocus.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;

    return BlocProvider.value(
      value: _changePasswordCubit,
      child: BlocListener<ChangePasswordCubit, ChangePasswordState>(
        listener: (context, state) {
          if (state.status.isSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Password changed successfully'),
                backgroundColor: themeData.primaryGreen500,
              ),
            );
            context.pop();
          } else if (state.status.isFailure) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.errorMessage),
                backgroundColor: themeData.red500,
              ),
            );
          }
        },
        child: Scaffold(
          backgroundColor: themeData.neutral50,
          appBar: const BaseAppBar(
            title: 'Change password',
            centerTitle: false,
          ),
          body: GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            behavior: HitTestBehavior.opaque,
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const SizedBox(height: 24),
                        _buildPasswordForm(),
                        const SizedBox(height: 24),
                        _buildPasswordValidation(),
                        const SizedBox(height: 24),
                        _buildFaceIdToggle(),
                      ],
                    ),
                  ),
                ),
                _buildBottomButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPasswordForm() {
    return BlocBuilder<ChangePasswordCubit, ChangePasswordState>(
      builder: (context, state) {
        return Column(
          children: [
            // Current Password Field
            TTextField(
              textController: _currentPasswordController,
              focusNode: _currentPasswordFocus,
              hintText: 'Current password',
              labelText: 'Current password',
              obscureText: !state.isCurrentPasswordVisible,
              errorText:
                  state.currentPasswordError.isNotEmpty
                      ? state.currentPasswordError
                      : null,
              prefixIcon: IconButton(
                icon: Assets.icons.icLock2Svg_.svg(
                  colorFilter: ColorFilter.mode(
                    context.themeData.textSecondary,
                    BlendMode.srcIn,
                  ),
                ),
                onPressed: null,
              ),
              suffixIcon: IconButton(
                icon: Icon(
                  state.isCurrentPasswordVisible
                      ? Icons.visibility_off
                      : Icons.visibility,
                  color: context.themeData.textSecondary,
                ),
                onPressed: () {
                  _changePasswordCubit.toggleCurrentPasswordVisibility();
                },
              ),
              onChanged: (value) {
                _changePasswordCubit.updateCurrentPassword(value);
              },
            ),
            const SizedBox(height: 12),
            // Forgot Password Link
            Align(
              alignment: Alignment.centerRight,
              child: TextButton(
                onPressed: () {
                  // TODO: Navigate to forgot password
                },
                child: Text(
                  'Forgot Password?',
                  style: titleSmall.copyWith(
                    color: context.themeData.textSecondary,
                  ),
                ),
              ),
            ),
            const SizedBox(height: 18),
            // New Password Field
            TTextField(
              textController: _newPasswordController,
              focusNode: _newPasswordFocus,
              hintText: 'New password',
              labelText: 'New password',
              obscureText: !state.isNewPasswordVisible,
              errorText:
                  state.newPasswordError.isNotEmpty
                      ? state.newPasswordError
                      : null,
              prefixIcon: IconButton(
                icon: Assets.icons.icLock2Svg_.svg(
                  colorFilter: ColorFilter.mode(
                    context.themeData.textSecondary,
                    BlendMode.srcIn,
                  ),
                ),
                onPressed: null,
              ),
              suffixIcon: IconButton(
                icon: Icon(
                  state.isNewPasswordVisible
                      ? Icons.visibility_off
                      : Icons.visibility,
                  color: context.themeData.textSecondary,
                ),
                onPressed: () {
                  _changePasswordCubit.toggleNewPasswordVisibility();
                },
              ),
              onChanged: (value) {
                _changePasswordCubit.updateNewPassword(value);
              },
            ),
            const SizedBox(height: 18),
            // Confirm Password Field
            TTextField(
              textController: _confirmPasswordController,
              focusNode: _confirmPasswordFocus,
              hintText: 'Confirm password',
              labelText: 'Confirm password',
              obscureText: !state.isConfirmPasswordVisible,
              errorText:
                  state.confirmPasswordError.isNotEmpty
                      ? state.confirmPasswordError
                      : null,
              prefixIcon: IconButton(
                icon: Assets.icons.icLock2Svg_.svg(
                  colorFilter: ColorFilter.mode(
                    context.themeData.textSecondary,
                    BlendMode.srcIn,
                  ),
                ),
                onPressed: null,
              ),
              suffixIcon: IconButton(
                icon: Icon(
                  state.isConfirmPasswordVisible
                      ? Icons.visibility_off
                      : Icons.visibility,
                  color: context.themeData.textSecondary,
                ),
                onPressed: () {
                  _changePasswordCubit.toggleConfirmPasswordVisibility();
                },
              ),
              onChanged: (value) {
                _changePasswordCubit.updateConfirmPassword(value);
              },
            ),
          ],
        );
      },
    );
  }

  Widget _buildPasswordValidation() {
    return BlocBuilder<ChangePasswordCubit, ChangePasswordState>(
      builder: (context, state) {
        return PasswordValidationList(
          have8Characters: state.have8Characters,
          containsUppercase: state.containsUppercase,
          haveSpecialCharacter: state.haveSpecialCharacter,
        );
      },
    );
  }

  Widget _buildFaceIdToggle() {
    return BlocBuilder<ChangePasswordCubit, ChangePasswordState>(
      builder: (context, state) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Unlock with Face ID?',
              style: titleMedium.copyWith(
                color: context.themeData.textSecondary,
              ),
            ),
            Switch(
              value: state.isEnableFaceId,
              onChanged: (value) {
                _changePasswordCubit.toggleFaceId(value);
              },
              activeColor: context.themeData.primaryGreen500,
            ),
          ],
        );
      },
    );
  }

  Widget _buildBottomButton() {
    return BlocBuilder<ChangePasswordCubit, ChangePasswordState>(
      builder: (context, state) {
        return Container(
          padding: EdgeInsets.only(
            left: 16,
            right: 16,
            bottom: MediaQuery.of(context).padding.bottom + 16,
            top: 16,
          ),
          child: TSButton.primary(
            title: 'Continue',
            isEnabled: state.isFormValid,
            isLoading: state.status.isLoading,
            onPressed: () {
              _changePasswordCubit.changePassword();
            },
          ),
        );
      },
    );
  }
}
