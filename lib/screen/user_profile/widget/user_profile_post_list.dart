import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_gen_ai_chat_ui/flutter_gen_ai_chat_ui.dart';
import 'package:toii_social/cubit/post/user_post/user_post_cubit.dart';
import 'package:toii_social/cubit/post/user_post/user_post_state.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/locator/locator.dart';
import 'package:toii_social/model/user/user_model.dart';
import 'package:toii_social/screen/home/<USER>/home_item_post_widget.dart';
import 'package:toii_social/screen/user_profile/widget/user_profile_nft_tab.dart';
import 'package:toii_social/screen/user_profile/widget/user_profile_repost_card.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/images/avatar_widget.dart';
import 'package:toii_social/widget/tabbar/base_tabbar.dart';

class UserProfilePostList extends StatefulWidget {
  const UserProfilePostList({super.key, this.user});
  final UserModel? user;
  @override
  State<UserProfilePostList> createState() => _UserProfilePostListState();
}

class _UserProfilePostListState extends State<UserProfilePostList> {
  final tabs = ["Posts", "Repost", "Collected NFT"];
  late final UserPostCubit _userPostCubit;

  @override
  void initState() {
    super.initState();
    _userPostCubit = UserPostCubit(socialRepository: serviceLocator());
    _loadInitialData();
  }

  void _loadInitialData() {
    if (widget.user?.id != null) {
      _userPostCubit.getUserPosts(widget.user!.id);
    }
  }

  void _handleTabChange(int index) {
    if (widget.user?.id == null) return;

    if (index == 0) {
      _userPostCubit.getUserPosts(widget.user!.id);
    } else if (index == 1) {
      _userPostCubit.getUserReposts(widget.user!.id);
    }
  }

  Widget _buildPostsTab(UserPostState state) {
    if (state.postStatus.isLoading) {
      return const Padding(
        padding: EdgeInsets.symmetric(vertical: 24.0),
        child: Center(child: LoadingWidget()),
      );
    } else if (state.postStatus.isLoaded && state.posts != null) {
      final posts = state.posts!.posts;
      if (posts.isEmpty) {
        return SizedBox(
          height: MediaQuery.of(context).size.height - 100,
          width: double.infinity,
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 24.0),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Assets.icons.icEmptyItem.svg(),
                  const SizedBox(height: 16),
                  Text('No posts found'),
                ],
              ),
            ),
          ),
        );
      }
      return Column(
        children:
            posts
                .map(
                  (post) => HomeItemPostWidget(
                    post: post,
                    isNotOnTap: false,
                    isShowActionMore: true,
                  ),
                )
                .toList(),
      );
    } else if (state.postStatus.isError) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 24.0),
        child: Center(child: Text('Error: ${state.errorMessage}')),
      );
    }
    return const Padding(
      padding: EdgeInsets.symmetric(vertical: 24.0),
      child: Center(child: LoadingWidget()),
    );
  }

  Widget _buildRepostsTab(UserPostState state) {
    if (state.repostStatus.isLoading) {
      return const Padding(
        padding: EdgeInsets.symmetric(vertical: 24.0),
        child: Center(child: LoadingWidget()),
      );
    } else if (state.repostStatus.isLoaded && state.reposts != null) {
      final reposts = state.reposts!.posts;
      if (reposts.isEmpty) {
        return SizedBox(
          height: MediaQuery.of(context).size.height - 100,
          width: double.infinity,
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 24.0),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Assets.icons.icEmptyItem.svg(),
                  const SizedBox(height: 16),
                  Text('No reposts found'),
                ],
              ),
            ),
          ),
        );
      }
      return Column(
        children:
            reposts.map((post) => UserProfileRepostCard(post: post)).toList(),
      );
    } else if (state.repostStatus.isError) {
      return Padding(
        padding: const EdgeInsets.symmetric(vertical: 24.0),
        child: Center(child: Text('Error: ${state.errorMessage}')),
      );
    }
    return const Padding(
      padding: EdgeInsets.symmetric(vertical: 24.0),
      child: Center(child: LoadingWidget()),
    );
  }

  Widget _buildProfileHeader() {
    return SizedBox(
      height: 70,
      child: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            AvatarWidget(
              size: 32,
              imageUrl: widget.user?.avatarUrl,
              name: widget.user?.fullName ?? '',
            ),

            const SizedBox(width: 16),

            Text(
              widget.user?.username ?? widget.user?.fullName ?? '',
              style: titleMedium.copyWith(
                color: context.themeData.neutral800,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(width: 16),
            Assets.icons.icScrollUp.svg(),
            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;

    return BlocProvider(
      create: (context) => _userPostCubit,
      child: BlocBuilder<UserPostCubit, UserPostState>(
        bloc: _userPostCubit,
        builder: (context, state) {
          return SingleChildScrollView(
            child: Column(
              children: [
                // Profile header with background and avatar

                // Tab bar and content
                BaseTabBar(
                  tabTitles: tabs,
                  tabContents: [
                    _buildPostsTab(state),
                    _buildRepostsTab(state),
                    const UserProfileNftTab(),
                  ],
                  selectedTabColor: themeData.primaryGreen500,
                  unselectedTabColor: themeData.neutral400,
                  tabTextStyle: titleLarge,
                  tabBarHeight: 48.0,
                  tabContentHeight: null, // Flexible height
                  onTabChanged: _handleTabChange,
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
