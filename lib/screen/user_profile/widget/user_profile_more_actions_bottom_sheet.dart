import 'package:flutter/material.dart';
import 'package:share_plus/share_plus.dart';
import 'package:toii_social/core/service/deep_link_service.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/user/user_model.dart';
import 'package:toii_social/screen/home/<USER>/bottom_sheet_report.dart';
import 'package:toii_social/widget/bottom_sheet/tt_bottom_sheet.dart';
import 'package:toii_social/widget/button/button.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';

class UserProfileMoreActionsBottomSheet extends StatelessWidget {
  const UserProfileMoreActionsBottomSheet({
    super.key,
    this.user,
    this.onFollow,
    this.onBlock,
    this.onReport,
    this.onShare,
  });

  final UserModel? user;
  final VoidCallback? onFollow;
  final VoidCallback? onBlock;
  final VoidCallback? onReport;
  final VoidCallback? onShare;

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const SizedBox(height: 12),

        // Follow action
        _ActionItem(
          icon: Assets.icons.icProfile2user.svg(
            width: 24,
            height: 24,
            colorFilter: ColorFilter.mode(
              themeData.neutral400,
              BlendMode.srcIn,
            ),
          ),
          text: 'Follow',
          onTap: () {
            Navigator.pop(context);
            _handleFollow(context);
          },
        ),

        // Block action
        _ActionItem(
          icon: Assets.icons.icBlock.svg(
            width: 24,
            height: 24,
            colorFilter: ColorFilter.mode(
              themeData.neutral400,
              BlendMode.srcIn,
            ),
          ),
          text: 'Block',
          onTap: () {
            Navigator.pop(context);
            _handleBlock(context);
          },
        ),

        // Report action
        _ActionItem(
          icon: Assets.icons.icWarning.svg(
            width: 24,
            height: 24,
            colorFilter: ColorFilter.mode(
              themeData.neutral400,
              BlendMode.srcIn,
            ),
          ),
          text: 'Report comment',
          onTap: () {
            Navigator.pop(context);
            _handleReport(context);
          },
        ),

        // Share action
        _ActionItem(
          icon: Assets.icons.icShare.svg(
            width: 24,
            height: 24,
            colorFilter: ColorFilter.mode(
              themeData.neutral400,
              BlendMode.srcIn,
            ),
          ),
          text: 'Share this profile',
          onTap: () {
            Navigator.pop(context);
            _handleShare(context);
          },
        ),

        const SizedBox(height: 24),

        // Cancel button
        TSButton(
          elevatedVariant: ButtonVariant.black,
          title: 'Cancel',
          size: ButtonSize.block,
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ],
    );
  }

  void _handleFollow(BuildContext context) {
    // TODO: Implement follow functionality
    onFollow?.call();
    context.showSnackbar(
      message: 'Follow functionality will be implemented',
      duration: const Duration(seconds: 2),
    );
  }

  void _handleBlock(BuildContext context) {
    // TODO: Implement block functionality
    onBlock?.call();
    context.showSnackbar(
      message: 'Block functionality will be implemented',
      duration: const Duration(seconds: 2),
    );
  }

  void _handleReport(BuildContext context) {
    // Show report bottom sheet
    showReportBottomSheet(
      context: context,
      postId: user?.id, // Using user ID as the reported item
      onReport: (reasons, customReason) {
        onReport?.call();
        context.showSnackbar(
          message: 'Report submitted successfully',
          duration: const Duration(seconds: 2),
        );
      },
    );
  }

  void _handleShare(BuildContext context) {
    if (user != null) {
      // Generate profile link
      final String profileUrl = DeepLinkService.generateUserLink(user!.id);
      final String userName =
          user!.fullName?.isNotEmpty == true
              ? user!.fullName!
              : user!.username.isNotEmpty == true
              ? user!.username
              : 'this user';
      final String shareText = '''Check out $userName's profile on Gao Social:

$profileUrl

#GaoSocial #Profile''';

      // Share the profile
      SharePlus.instance.share(
        ShareParams(
          text: shareText,
          subject: 'Check out this profile on Gao Social',
        ),
      );

      onShare?.call();
    } else {
      context.showSnackbar(
        message: 'Unable to share profile',
        duration: const Duration(seconds: 2),
      );
    }
  }
}

class _ActionItem extends StatelessWidget {
  final Widget icon;
  final String text;
  final VoidCallback onTap;

  const _ActionItem({
    required this.icon,
    required this.text,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;

    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 16),
        child: Row(
          children: [
            SizedBox(width: 24, height: 24, child: icon),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                text,
                style: titleMedium.copyWith(
                  color: themeData.neutral800,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Helper function to show the user profile more actions bottom sheet
Future<void> showUserProfileMoreActionsBottomSheet({
  required BuildContext context,
  UserModel? user,
  VoidCallback? onFollow,
  VoidCallback? onBlock,
  VoidCallback? onReport,
  VoidCallback? onShare,
}) {
  return showTtBottomSheet(
    context,
    child: UserProfileMoreActionsBottomSheet(
      user: user,
      onFollow: onFollow,
      onBlock: onBlock,
      onReport: onReport,
      onShare: onShare,
    ),
  );
}
