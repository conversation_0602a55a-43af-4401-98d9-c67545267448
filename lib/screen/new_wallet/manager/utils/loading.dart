import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:toii_social/model/asset/types.dart';
import 'package:flutter_feather_icons/flutter_feather_icons.dart';
import 'package:fluttertoast/fluttertoast.dart';

class LoadingHelper {
  static OverlayEntry? _overlay;

  static void show(BuildContext context, AppColors colors, [String? message]) {
    if (_overlay != null) return;

    // Dismiss keyboard
    FocusManager.instance.primaryFocus?.unfocus();

    _overlay = OverlayEntry(
      builder: (context) => Material(
        color: Colors.black87,
        child: Center(
          child: Container(
            padding: const EdgeInsets.all(14),
            decoration: BoxDecoration(
              color: colors.secondaryColor,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Row(
              spacing: 10,
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                standardCircularProgressIndicator(colors: colors),
                if (message != null) ...[
                  const SizedBox(height: 16),
                  Text(
                    message,
                    style: GoogleFonts.roboto(color: colors.textColor),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );

    Overlay.of(context).insert(_overlay!);
  }

  static void hide() {
    _overlay?.remove();
    _overlay = null;
  }
}

extension FutureWithLoading<T> on Future<T> {
  Future<T> withLoading(BuildContext context, AppColors colors,
      [String? message]) {
    LoadingHelper.show(context, colors, message);
    return then((value) {
      Future.delayed(const Duration(milliseconds: 1000), LoadingHelper.hide);
      return value;
    }).catchError((error) {
      Future.delayed(const Duration(milliseconds: 1000), LoadingHelper.hide);
      throw error;
    });
  }
}
Widget standardCircularProgressIndicator(
    {double width = 25,
    double height = 25,
    required AppColors colors,
    Color? color}) {
  return SizedBox(
    width: width,
    height: height,
    child: CircularProgressIndicator(
      color: color ?? colors.textColor,
    ),
  );
}
notifySuccess(String message, BuildContext context) => showCustomSnackBar(
    context: context, message: message, type: MessageType.success);
notifyError(String message, BuildContext context) => showCustomSnackBar(
    context: context, message: message, type: MessageType.error);

void showCustomSnackBar(
    {required BuildContext context,
    required String message,
    IconData icon = Icons.info,
    MessageType? type,
    Color? iconColor}) {
  FToast fToast = FToast();
  fToast.init(context);

  final title = Text(message, style: TextStyle(color: Colors.black87));
  // final shadowColor = const Color.fromARGB(18, 21, 21, 21);
  // final double borderRadius = 20;
  //final duration = Duration(milliseconds: 500);
  fToast.showToast(
      child: LayoutBuilder(builder: (ctx, c) {
        return Container(
          width: c.maxWidth,
          padding: EdgeInsets.symmetric(vertical: 5, horizontal: 20),
          decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.24),
                  offset: Offset(0, 2),
                  blurRadius: 4,
                ),
              ],
              borderRadius: BorderRadius.circular(5)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            spacing: 10,
            children: [
              ConstrainedBox(
                constraints: BoxConstraints(maxWidth: c.maxWidth * 0.67),
                child: title,
              ),
              IconButton(
                  onPressed: () => fToast.removeCustomToast(),
                  icon: Icon(
                    FeatherIcons.x,
                    color: const Color.fromARGB(226, 0, 0, 0),
                  ))
            ],
          ),
        );
      }),
      gravity: ToastGravity.TOP);
}

