
import 'package:toii_social/logger/logger.dart';
import 'package:toii_social/model/asset/account_related_types.dart';
import 'package:toii_social/screen/new_wallet/manager/db/global_database.dart';

class ListAddressDynamicDb {
  final _db = GlobalDatabase();
  final PublicAccount account;
  final Crypto crypto;
  ListAddressDynamicDb({required this.account, required this.crypto});

  String get cryptoId =>
      (crypto.isNative ? crypto.chainId.toString() : crypto.contractAddress) ??
      crypto.cryptoId;
  String get dataKey =>
      "user/${account.addressByToken(crypto).trim().toLowerCase()}/last_used_addresses/$cryptoId/addresses-database";

  Future<bool> saveData(List<String> addresses) async {
    try {
      await _db.saveDynamicData(data: addresses, key: dataKey);
      return true;
    } catch (e) {
      logError(e.toString());
      return false;
    }
  }

  Future<List<dynamic>> getData() async {
    try {
      final savedData = await _db.getDynamicData(key: dataKey);
      return (savedData is List ? savedData : savedData) ?? [];
    } catch (e) {
      logError(e.toString());
      return [];
    }
  }
}
