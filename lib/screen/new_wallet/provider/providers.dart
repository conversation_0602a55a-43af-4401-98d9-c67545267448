import 'dart:developer';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:toii_social/model/asset/account_related_types.dart' as types;
import 'package:toii_social/model/asset/account_related_types.dart';
import 'package:toii_social/screen/new_wallet/app_components_states/assets_load_state.dart';
import 'package:toii_social/screen/new_wallet/manager/db/crypto_storage_manager.dart';
import 'package:toii_social/screen/new_wallet/manager/db/wallet_db.dart';
import 'package:toii_social/screen/new_wallet/manager/db/wallet_db_stateless.dart';
import 'package:toii_social/screen/new_wallet/notifiers/accounts_notifier.dart';
import 'package:toii_social/screen/new_wallet/notifiers/assets_notifier.dart';
import 'package:toii_social/screen/new_wallet/notifiers/last_account_notifier.dart';
import 'package:toii_social/screen/new_wallet/notifiers/nodes_notifier.dart';
import 'package:toii_social/screen/new_wallet/notifiers/saved_crypto.dart';
import 'package:toii_social/screen/new_wallet/notifiers/wallet_manager_notifier.dart';
import 'package:toii_social/utils/keychain/keychain_service.dart';

final cryptoStorageProvider = Provider((ref) => (CryptoStorageManager()));

final walletSaverProvider = Provider((ref) => WalletDatabase());

final lastConnectedKeyIdNotifierProvider =
    AsyncNotifierProvider<LastConnectedKeyIdNotifier, String?>(
      LastConnectedKeyIdNotifier.new,
    );

final internetConnectionProvider = Provider<InternetConnection>(
  (ref) => InternetConnection(),
);

final accountsNotifierProvider =
    AsyncNotifierProvider<AccountsNotifier, List<types.PublicAccount>>(
      AccountsNotifier.new,
    );

final web3ProviderNotifier = Provider<WalletManagerNotifier>((ref) {
  return WalletManagerNotifier(ref);
});

final assetsNotifierProvider =
    AsyncNotifierProvider<AssetsNotifier, List<types.Asset>>(
      AssetsNotifier.new,
    );

final savedCryptosProviderNotifier =
    AsyncNotifierProvider<SavedCryptoProvider, List<types.Crypto>>(
      SavedCryptoProvider.new,
    );

final currentAccountProvider = FutureProvider<types.PublicAccount?>((
  ref,
) async {
  final accounts = await ref.watch(accountsNotifierProvider.future);
  final lastKeyId = await ref.watch(lastConnectedKeyIdNotifierProvider.future);
  if (accounts.isEmpty) {
    final saver = WalletDatabase();
    saver.saveFirst();
    throw ("No account found");
  }

  if (lastKeyId != null) {
    final accountFounded = accounts.firstWhere(
      (acc) => acc.keyId.toLowerCase().trim() == lastKeyId.toLowerCase().trim(),
      orElse: () => accounts.first,
    );
    log("Last account id: $lastKeyId");

    log("Account founded: ${accountFounded.toJson()}");
    return accountFounded;
  }
  return accounts.firstOrNull;
});

final getSavedAssetsProvider = FutureProvider<List<types.Asset>?>((ref) async {
  final cryptoStorage = ref.watch(cryptoStorageProvider);
  final account = await ref.watch(currentAccountProvider.future);
  final assetsLoadState = ref.watch(assetsLoadStateProvider.notifier);

  if (account != null) {
    log("Getting saved assets");
    final savedAssets = await cryptoStorage.getSavedAssets(wallet: account);
    log("Saved Assets len ${savedAssets?.length}");

    return savedAssets;
  }
  return null;
});

final assetsLoadStateProvider =
    StateNotifierProvider<AssetsLoadState, AssetNotification>(
      (ref) => AssetsLoadState(),
    );

final nodesProvider = AsyncNotifierProvider<NodesNotifier, types.Nodes>(() {
  return NodesNotifier();
});
