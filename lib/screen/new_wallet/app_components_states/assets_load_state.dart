import 'package:flutter_riverpod/flutter_riverpod.dart';

enum AssetNotificationState { loading, completed, error }

class AssetNotification {
  final AssetNotificationState state;

  bool get isLoading => state == AssetNotificationState.loading;
  bool get isCompleted => state == AssetNotificationState.completed;
  bool get isError => state == AssetNotificationState.error;

  AssetNotification({required this.state});
}

class AssetsLoadState extends StateNotifier<AssetNotification> {
  AssetsLoadState()
      : super(AssetNotification(state: AssetNotificationState.loading));

  void updateState(AssetNotificationState newState) {
    state = AssetNotification(state: newState);
  }
}
