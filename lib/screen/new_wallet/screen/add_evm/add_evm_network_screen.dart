import 'package:flutter/material.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/model/asset/account_related_types.dart';
import 'package:toii_social/screen/new_wallet/manager/utils/app_utils.dart';
import 'package:toii_social/screen/new_wallet/manager/utils/loading.dart';
import 'package:toii_social/screen/new_wallet/manager/web3_interactions/evm/web3_client.dart';
import 'package:toii_social/widget/button/button.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class AddEvmNetworkScreen extends StatefulWidget {
  const AddEvmNetworkScreen({super.key});

  @override
  State<AddEvmNetworkScreen> createState() => _AddEvmNetworkScreenState();
}

class _AddEvmNetworkScreenState extends State<AddEvmNetworkScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _symbolController = TextEditingController();
  final _chainIdController = TextEditingController();
  final _rpcUrlController = TextEditingController();
  final _explorerController = TextEditingController();

  @override
  void dispose() {
    _nameController.dispose();
    _symbolController.dispose();
    _chainIdController.dispose();
    _rpcUrlController.dispose();
    _explorerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Header with back arrow, title, and check button
          Container(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: themeData.neutral100,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.arrow_back_ios_new,
                      size: 18,
                      color: Colors.black,
                    ),
                  ),
                ),
                Expanded(
                  child: Center(
                    child: Text(
                      'Add Network',
                      style: titleLarge.copyColor(themeData.neutral800),
                    ),
                  ),
                ),
                Container(width: 40, height: 40),
              ],
            ),
          ),

          // Form Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Form(
                key: _formKey,
                child: Column(
                  children: [
                    const SizedBox(height: 20),

                    // Name Field
                    _buildInputField(
                      controller: _nameController,
                      label: 'Name',
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter network name';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 20),

                    // Symbol Field
                    _buildInputField(
                      controller: _symbolController,
                      label: 'Symbol',
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter symbol';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 20),

                    // Chain ID Field
                    _buildInputField(
                      controller: _chainIdController,
                      label: 'Chain Id',
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null ||
                            value.isEmpty ||
                            int.tryParse(value) == null) {
                          return "Chain id is required";
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 20),

                    // RPC URL Field
                    _buildInputField(
                      controller: _rpcUrlController,
                      label: 'Rpc Url',
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter RPC URL';
                        }
                        final uri = Uri.tryParse(value);
                        if (uri == null || !uri.hasAbsolutePath) {
                          return 'Please enter a valid URL';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: 20),

                    // Explorer Field (Optional)
                    _buildInputField(
                      controller: _explorerController,
                      label: 'Explorer (optional)',
                    ),
                    const SizedBox(height: 20),
                    TSButton.primary(
                      onPressed: _saveNetwork,
                      title: 'Save Network',
                    ),

                    const SizedBox(height: 40),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputField({
    required TextEditingController controller,
    required String label,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: const Color(
          0xFFF5F5F5,
        ), // Light gray background matching the image
        borderRadius: BorderRadius.circular(12),
      ),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType,
        validator: validator,
        style: bodyMedium.copyColor(themeData.neutral800),
        decoration: InputDecoration(
          labelText: label,
          labelStyle: bodyMedium.copyColor(themeData.neutral500),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(16),
          floatingLabelBehavior: FloatingLabelBehavior.auto,
        ),
      ),
    );
  }

  void _saveNetwork() async {
    if (_formKey.currentState?.validate() == true) {
      final web3Client = DynamicWeb3Client(rpcUrl: _rpcUrlController.text);
      final chainId = await web3Client.getChainId();
      if (chainId == null) {
        notifyError("Invalid Rpc Url", context);
      } else if (chainId != int.tryParse(_chainIdController.text)) {
        notifyError(
          "RPC URL points to $chainId not ${_chainIdController.text} ",
          context,
        );
      } else {
        final newCrypto = Crypto(
          networkType: NetworkType.evm,
          name: _nameController.text,
          color: Colors.grey,
          type: CryptoType.native,
          decimals: 18,
          cryptoId: AppUtils.generateUUID(),
          canDisplay: true,
          symbol: _symbolController.text,
          rpcUrls: [_rpcUrlController.text],
          explorers: [_explorerController.text],
          chainId: int.parse(_chainIdController.text),
        );
        Navigator.pop(context, newCrypto);
      }
    }
  }
}

// Function to show the bottom sheet
Future<dynamic> showAddNetworkBottomSheet(BuildContext context) {
 return showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    backgroundColor: Colors.transparent,
    builder: (context) => const AddEvmNetworkScreen(),
  );
}
