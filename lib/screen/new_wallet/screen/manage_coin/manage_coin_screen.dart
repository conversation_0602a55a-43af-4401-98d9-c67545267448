import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/logger/logger.dart';
import 'package:toii_social/model/asset/account_related_types.dart';
import 'package:toii_social/screen/new_wallet/manager/db/crypto_storage_manager.dart';
import 'package:toii_social/screen/new_wallet/manager/db/wallet_db.dart';
import 'package:toii_social/screen/new_wallet/manager/manager/crypto_manager.dart';
import 'package:toii_social/screen/new_wallet/manager/utils/encrypt_service.dart';
import 'package:toii_social/screen/new_wallet/manager/utils/loading.dart';
import 'package:toii_social/screen/new_wallet/provider/providers.dart';
import 'package:toii_social/screen/new_wallet/screen/add_evm/add_evm_network_screen.dart';
import 'package:toii_social/screen/new_wallet/screen/widget/crypto_picture.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/scaffords/scafford.dart';

class ManageCoinScreen extends StatefulHookConsumerWidget {
  const ManageCoinScreen({super.key});

  @override
  ConsumerState<ManageCoinScreen> createState() => _ManageCoinScreenState();
}

class _ManageCoinScreenState extends ConsumerState<ManageCoinScreen> {
  final cryptoStorageManager = CryptoStorageManager();
  List<PublicAccount> accounts = [];
  final web3Manager = WalletDatabase();
  final encryptService = EncryptService();

  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentAccountAsync = ref.watch(currentAccountProvider);
    final savedCryptoProvider = ref.watch(
      savedCryptosProviderNotifier.notifier,
    );
    final savedCryptoAsync = ref.watch(savedCryptosProviderNotifier);
    final accountsProvider = ref.watch(accountsNotifierProvider);
    final cryptoManager = CryptoManager();
    final nodeNotifier = ref.watch(nodesProvider);
    final nodes = useState<Nodes>(Nodes(nodes: []));
    final allCryptos = useState<List<Crypto>>([]);
    final currentAccount = useState<PublicAccount?>(null);
    useEffect(() {
      nodeNotifier.whenData((data) {
        nodes.value = data;
      });
      return null;
    }, [nodeNotifier]);

    useEffect(() {
      Future<void> getListDefaultTokens() async {
        try {
          final defaultTokens = await cryptoManager.getDefaultTokens();
          final savedTokens = savedCryptoAsync.value ?? [];
          final uniqueTokens = cryptoManager.addOnlyNewTokens(
            localList: savedTokens,
            externalList: defaultTokens,
          );
          allCryptos.value = uniqueTokens;
        } catch (e) {
          logError(e.toString());
        }
      }

      getListDefaultTokens();
      return null;
    }, [savedCryptoAsync]);
    accountsProvider.whenData(
      (data) => {
        setState(() {
          accounts = data;
        }),
      },
    );

    currentAccountAsync.whenData((value) => currentAccount.value = value);

    List<Crypto> getCryptoList() {
      final listTokens =
          allCryptos.value
              .where(
                (c) =>
                    c.symbol.toLowerCase().contains(
                      _searchController.text.toLowerCase(),
                    ) ||
                    c.name.toLowerCase().contains(
                      _searchController.text.toLowerCase(),
                    ),
              )
              .toList();

      final account = currentAccount.value;
      if (account == null) {
        throw Exception("No account found");
      }

      if (account.origin.isPrivateKey || account.origin.isPublicAddress) {
        return listTokens
            .where((c) => account.supportedNetworks.contains(c.getNetworkType))
            .toList();
      }

      return listTokens;
    }

    Future<void> addNetwork(Crypto newCrypto) async {
      try {
        {
          final saveResult = await savedCryptoProvider.addCrypto(newCrypto);
          final node = Node(
            chainType: newCrypto.networkType ?? NetworkType.evm,
            rpcUrl: newCrypto.rpcUrls!.first,
            chainId: newCrypto.chainId!,
          );
          ref.read(nodesProvider.notifier).addNode(node: node);

          if (saveResult) {
            notifySuccess('Network added successfully.', context);

            Navigator.pop(context);
          } else {
            notifyError('Error adding token.', context);
            Navigator.pop(context);
          }
        }
      } catch (e) {
        logError(e.toString());
        notifyError(e.toString(), context);
      }
    }

    void showWalletActions() {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) {
          return SafeArea(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: themeData.white900,
                borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  GestureDetector(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Material(
                      color: themeData.neutral200,
                      borderRadius: BorderRadius.circular(8),
                      child: ListTile(
                        visualDensity: VisualDensity(
                          horizontal: 0,
                          vertical: 0,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        leading: Icon(
                          size: 20,
                          Icons.add,
                          color: themeData.neutral800,
                        ),
                        title: Text(
                          "Add custom token",
                          style: bodyMedium.copyWith(
                            color: themeData.neutral800,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  GestureDetector(
                    onTap: () async {
                      final newNetwork = await showAddNetworkBottomSheet(
                        context,
                      );
                      if (newNetwork != null) {
                        if (allCryptos.value.any(
                          (c) => c.chainId == newNetwork.chainId,
                        )) {
                          notifyError("Network already exist", context);
                          return;
                        }
                        addNetwork(newNetwork);
                      }
                    },
                    child: Material(
                      color: themeData.neutral200,
                      borderRadius: BorderRadius.circular(8),
                      child: ListTile(
                        visualDensity: VisualDensity(
                          horizontal: 0,
                          vertical: 0,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        leading: Icon(
                          size: 20,
                          Icons.construction,
                          color: themeData.neutral800,
                        ),
                        title: Text(
                          "Add EVM network",
                          style: bodyMedium.copyWith(
                            color: themeData.neutral800,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      );
    }

    final theme = Theme.of(context);

    return BaseScaffold(
      showLeading: true,
      title: Text(
        'Manage Coins',
        style: titleLarge.copyColor(theme.neutral800),
      ),
      actions: [
        IconButton(
          onPressed: () => showWalletActions(),
          icon: Icon(LucideIcons.plus, color: themeData.neutral800, size: 20),
        ),
      ],
      body: Column(
        children: [
          const SizedBox(height: 16),
          // Search bar
          _buildSearchBar(context),

          const SizedBox(height: 16),

          // Token list
          Expanded(
            child: ListView.separated(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: getCryptoList().length,
              separatorBuilder:
                  (context, index) => Container(
                    height: 1,
                    color: theme.neutral200,
                    margin: const EdgeInsets.symmetric(vertical: 8),
                  ),
              itemBuilder: (context, index) {
                final reorganized =
                    getCryptoList()..sort(
                      (a, b) => cryptoManager
                          .cleanName(a.symbol.trim())
                          .compareTo(cryptoManager.cleanName(b.symbol.trim())),
                    );

                final crypto = reorganized[index];
                return _buildTokenItem(
                  context,
                  cryptoManager,
                  crypto,
                  savedCryptoAsync,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: themeData.black50,
        borderRadius: BorderRadius.circular(100),
      ),
      child: Row(
        children: [
          SvgPicture.asset(
            Assets.icons.icSearch.path,
            colorFilter: ColorFilter.mode(
              themeData.neutral400,
              BlendMode.srcIn,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: TextField(
              controller: _searchController,
              style: bodyMedium.copyWith(color: themeData.textPrimary),
              decoration: InputDecoration(
                hintText: 'Search token symbol',
                hintStyle: bodyMedium.copyWith(color: themeData.textSecondary),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTokenItem(
    BuildContext context,
    CryptoManager cryptoManager,
    Crypto token,
    AsyncValue<List<Crypto>> savedCryptoAsync,
  ) {
    final theme = Theme.of(context);

    return GestureDetector(
      //- onTap: () => _selectToken(context, token),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            // Token icon
         //   CryptoPicture(crypto: token, size: 40),

            const SizedBox(width: 12),

            // Token info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    token.name,
                    style: bodyMedium.copyWith(
                      color: theme.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    token.symbol,
                    style: bodySmall.copyWith(color: theme.textSecondary),
                  ),
                ],
              ),
            ),
            CupertinoSwitch(
              // overrides the default green color of the track
              activeTrackColor: themeData.primaryGreen500,
              // color of the round icon, which moves from right to left
              thumbColor: themeData.white900,
              // when the switch is off
              inactiveTrackColor: Colors.black12,
              // boolean variable value
              value: cryptoManager.isEnabled(
                token,
                savedCryptoAsync.value ?? [],
              ),
              // changes the state of the switch
              onChanged: (newVal) async {
                try {
                  final result = await ref
                      .read(savedCryptosProviderNotifier.notifier)
                      .toggleCanDisplay(token, newVal);

                  if (result) {
                    log("State changed successfully");
                  } else {
                    log("Error changing state");
                  }
                } catch (e) {
                  logError(e.toString());
                  notifyError(e.toString(), context);
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
