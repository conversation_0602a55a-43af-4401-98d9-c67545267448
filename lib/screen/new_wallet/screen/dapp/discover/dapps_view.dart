import 'package:flutter/material.dart';
import 'package:toii_social/model/asset/browser.dart';
import 'package:toii_social/model/asset/news_types.dart' hide Category;

import 'network_image.dart';

class DappsViewList extends StatelessWidget {
  final Category category;
  final List<DApp> primaryDapps;
  final List<DApp> nonPrimaryDapps;
  final void Function(DApp)? onSelect;

  const DappsViewList({
    super.key,
    required this.category,

    required this.nonPrimaryDapps,
    required this.primaryDapps,
    required this.onSelect,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = TextTheme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 20),
      child: Column(
        spacing: 10,
        children: [
          Column(
            spacing: 10,
            children: [
              Row(
                spacing: 10,
                children: [
                  CustomNetworkImage(
                    url: category.iconUrl,
                    size: 30,
                    imageSizeOf: 30,
                  ),
                  Text(
                    category.name,
                    style: textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      //   color: colors.textColor,
                      fontSize: 18,
                    ),
                  ),
                ],
              ),
              Text(
                category.description,
                style: textTheme.bodyMedium?.copyWith(
                  fontSize: 14,
                 // color: Theme.of(context).textColor.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
          // Column(
          //   children: List.generate(primaryDapps.length, (primaryIndex) {
          //     final currentDapp = primaryDapps[primaryIndex];
          //     return ListTitleDescription(
          //       imageSizeOf: imageSizeOf,
          //       description: currentDapp.description,
          //       imageUrl: currentDapp.imageUrl,
          //       title: currentDapp.name,
          //       fontSizeOf: fontSizeOf,
          //       colors: colors,
          //       onTap: () => onSelect != null ? onSelect!(currentDapp) : null,
          //     );
          //   }),
          // ),
          SizedBox(height: 10),
          SizedBox(
            height: 100,
            child: ListView.separated(
              separatorBuilder: (context, index) {
                return Padding(padding: const EdgeInsets.all(5));
              },
              scrollDirection: Axis.horizontal,
              itemCount: nonPrimaryDapps.length,
              itemBuilder: (context, nonPrimaryIndex) {
                final currentNonPrimary = nonPrimaryDapps[nonPrimaryIndex];

                return Material(
                  color: Theme.of(context).primaryColor,
                  borderRadius: BorderRadius.circular(10),
                  child: InkWell(
                    borderRadius: BorderRadius.circular(10),
                    onTap:
                        () =>
                            onSelect != null
                                ? onSelect!(currentNonPrimary)
                                : null,
                    child: Container(
                      width: 100,
                      height: 100,
                      padding: const EdgeInsets.all(10),
                      child: Align(
                        alignment: Alignment.center,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          spacing: 5,
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(5),
                              child: CustomNetworkImage(
                                url: currentNonPrimary.imageUrl,
                                size: 50,
                                cover: true,
                                imageSizeOf: 18,
                              //  colors: colors,
                              ),
                            ),
                            Text(
                              currentNonPrimary.name,
                              style: textTheme.bodyMedium?.copyWith(
                                fontSize: 12,
                                overflow: TextOverflow.ellipsis,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
