import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/model/asset/account_related_types.dart';
import 'package:toii_social/model/asset/types.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/new_wallet/screen/widget/crypto_picture.dart';
import 'package:toii_social/utils/utils/number_formatter.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class TokenItemWidget extends StatelessWidget {
  final Asset token;
  const TokenItemWidget({super.key, required this.token});
  String formatDecimals(String value) {
    return NumberFormatter().formatDecimal(value);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        context.push(RouterEnums.tokenDetails.routeName, extra: token);
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Token icon
            // CryptoPicture(
            //   crypto: token.crypto,
            //   size: 40,
            // ),

            const SizedBox(width: 12),
            // Token info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        token.crypto.symbol.toUpperCase(),
                        style: bodyMedium.copyWith(
                          color: Theme.of(context).textPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(width: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 10,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Theme.of(context).black400,
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Text(
                          !token.crypto.isNative
                              ? "${token.crypto.network?.name}"
                              : token.crypto.name,
                          style: bodySmall.copyWith(
                            color: Theme.of(context).neutral200,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 2),
                  Row(
                    spacing: 2,
                    children: [
                      Text(
                        "\$${formatDecimals(token.cryptoPrice.toString())}",
                        style: bodyMedium.copyColor(themeData.neutral400),
                      ),
                      if (double.tryParse(token.cryptoTrendPercent) != 0)
                        Text(
                          " ${double.parse(token.cryptoTrendPercent).toStringAsFixed(2)}%",
                          style: bodyMedium.copyWith(
                            color:
                                double.parse(token.cryptoTrendPercent) > 0
                                    ? Colors.green
                                    : Colors.red,
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
            //  Token balance
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  // isCryptoHidden
                  //   ? "***"
                  NumberFormatter()
                      .formatValue(str: token.balanceCrypto)
                      .trim(),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  style: titleMedium.copyWith(color: themeData.neutral800),
                ),

                const SizedBox(height: 2),
                Text(
                  '\$${token.balanceUsd}',
                  style: bodyMedium.copyColor(themeData.neutral400),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
