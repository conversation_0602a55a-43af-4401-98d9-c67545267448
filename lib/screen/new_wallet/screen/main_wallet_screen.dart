import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:lucide_icons_flutter/lucide_icons.dart';
import 'package:toii_social/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/asset/account_related_types.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/new_wallet/provider/providers.dart';
import 'package:toii_social/screen/new_wallet/screen/widget/token_item_widget.dart';
import 'package:toii_social/utils/utils.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/scaffords/scafford.dart';

class MainWalletScreen extends StatefulHookConsumerWidget {
  const MainWalletScreen({super.key});

  @override
  ConsumerState<MainWalletScreen> createState() => _MainWalletScreenState();
}

class _MainWalletScreenState extends ConsumerState<MainWalletScreen>
    with SingleTickerProviderStateMixin {
  String searchCryptoQuery = "";
  List<Crypto> reorganizedCrypto = [];

  Widget _buildWalletAddressHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            GetIt.instance<ProfileCubit>().state.userModel?.username ?? "",
            style: titleLarge.copyColor(themeData.neutral800),
          ),
          Row(
            children: [
              Text(
                shortenAddress(
                  GetIt.instance<ProfileCubit>()
                          .state
                          .userModel
                          ?.walletAddress ??
                      "",
                ),
                style: bodyMedium.copyWith(
                  color: themeData.neutral800,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(width: 8),
              GestureDetector(
                onTap:
                    () => _copyToClipboard(
                      context,
                      GetIt.instance<ProfileCubit>()
                              .state
                              .userModel
                              ?.walletAddress ??
                          "",
                    ),
                child: Icon(Icons.copy, size: 16, color: themeData.neutral800),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceCard() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Stack(
        children: [
          Assets.images.bgWallet1.image(),
          Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [_buildWalletAddressHeader()],
                    ),
                    const Spacer(),

                    GestureDetector(
                      onTap: () {
                        context.push(RouterEnums.scanQr.routeName);
                      },
                      child: SvgPicture.asset(Assets.icons.icScanQr.path),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                //  Balance amount
                Text(
                  '\$${0.toStringAsFixed(2)}',
                  //'\$${state.totalBalance.toStringAsFixed(2)}',
                  style: headlineLarge.copyColor(themeData.black900),
                ),
                const SizedBox(height: 4),

                Text(
                  'Balance',
                  style: labelLarge.copyColor(themeData.black600),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildActionButton(
            path: Assets.icons.icSend.path,
            label: 'Send',
            onTap: () => _showSendDialog(context),
          ),
          _buildActionButton(
            path: Assets.icons.icReceive.path,
            label: 'Receive',
            onTap: () => _showReceiveDialog(context),
          ),
          _buildActionButton(
            path: Assets.icons.icEarn.path,
            label: 'Earn',
            onTap: () => _showHistoryScreen(context),
          ),
          _buildActionButton(
            path: Assets.icons.icDapp.path,
            label: 'DApps',
            onTap: () => _showDAppsScreen(context),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required String path,
    required String label,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              color: theme.neutral100,
              borderRadius: BorderRadius.circular(100),
            ),
            child: Center(child: SvgPicture.asset(path)),
          ),
          const SizedBox(height: 8),
          Text(label, style: bodyMedium.copyColor(themeData.neutral800)),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final accountsProvider = ref.watch(accountsNotifierProvider);
    //final providerNotifier = ref.watch(accountsNotifierProvider.notifier);
    final savedCryptoAsync = ref.watch(savedCryptosProviderNotifier);

    final currentAccount = ref.watch(currentAccountProvider).value;
    final assetsNotifier = ref.watch(assetsNotifierProvider);
    final savedAssetsProvider = ref.watch(getSavedAssetsProvider);
    final initialAssets = useState<List<Asset>>([]);

    final assets = useState<List<Asset>>([]);
    final isInitialized = useState<bool>(false);

    List<Asset> getFilteredCryptos() {
      return assets.value
          .where(
            (c) =>
                c.crypto.symbol.toString().toLowerCase().contains(
                  searchCryptoQuery,
                ) ||
                c.crypto.name.toString().toLowerCase().contains(
                  searchCryptoQuery,
                ) ||
                c.balanceUsd.toString().contains(searchCryptoQuery),
          )
          .toList();
    }

    useEffect(() {
      savedAssetsProvider.whenData((data) {
        if (data != null && data.isNotEmpty) {
          initialAssets.value = [...data];
          assets.value = [...data];
          //   updateTotalBalance(assets.value);
        }
        isInitialized.value = true;
      });
      return null;
    }, [savedAssetsProvider]);

    useEffect(() {
      assetsNotifier.whenData((data) {
        if (data.isNotEmpty) {
          initialAssets.value = [...data];
          assets.value = [...data];
        }
        //   updateTotalBalance(assets.value);
        isInitialized.value = true;
      });

      return null;
    }, [assetsNotifier]);
    savedCryptoAsync.whenData((data) {
      reorganizedCrypto = data.where((e) => e.canDisplay).toList();
    });
    return BaseScaffold(
      //    backgroundColor: theme.neutral50,
      title: Row(
        children: [
          Text(
            'My Wallet',
            style: headlineMedium.copyWith(
              color: themeData.neutral800,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
      body: SafeArea(
        child: CustomScrollView(
          slivers: <Widget>[
            SliverToBoxAdapter(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const SizedBox(height: 8),
                      _buildBalanceCard(),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          // IconButton(
                          //   onPressed: toggleHidden,
                          //   icon:
                          //       assetsState.isLoading
                          //           ? SizedBox(
                          //             width: 15,
                          //             height: 15,
                          //             child: CircularProgressIndicator(
                          //               color: colors.textColor,
                          //             ),
                          //           )
                          //           : (Icon(
                          //             uiConfig.value.isCryptoHidden
                          //                 ? LucideIcons.eyeClosed
                          //                 : Icons.remove_red_eye_outlined,
                          //             color: colors.textColor,
                          //             size: iconSizeOf(20),
                          //           )),
                          // ),
                        ],
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          //   Icon(FeatherIcons.dollarSign, color: colors.textColor, size: textTheme.headlineLarge?.fontSize,),
                          // Skeletonizer(
                          //   enabled: !isInitialized.value,
                          //   containersColor:
                          //       colors.secondaryColor,
                          //   child: Text(
                          //     !uiConfig.value.isCryptoHidden
                          //         ? "\$${NumberFormatter().formatValue(maxDecimals: 2, str: totalBalance.value)}"
                          //         : "***",
                          //     overflow: TextOverflow.clip,
                          //     maxLines: 1,
                          //     style: textTheme.headlineLarge
                          //         ?.copyWith(
                          //             fontSize:
                          //                 fontSizeOf(29.52),
                          //             color: colors.textColor),
                          //   ),
                          // )
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  // Action Buttons
                  _buildActionButtons(context),
                  const SizedBox(height: 24),
                  Align(
                    alignment: Alignment.center,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 20),
                      child: InkWell(
                        onTap: () {
                          context.push(RouterEnums.manageCoin.routeName);
                          // Navigator.push(
                          //   context,
                          //   MaterialPageRoute(
                          //     builder: (_) => AddCryptoView(),
                          //   ),
                          // );
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 10),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            spacing: 10,
                            children: [
                              Icon(
                                LucideIcons.bolt,
                                // color: colors.textColor.withValues(
                                //   alpha: 0.8,
                                // ),
                                size: 20,
                              ),
                              Text(
                                "Manage Coins",
                                // style: textTheme.bodyMedium?.copyWith(
                                //   fontSize: fontSizeOf(14),
                                //   color: colors.textColor.withValues(
                                //     alpha: 0.8,
                                //   ),
                                // ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SliverList(
              delegate: SliverChildBuilderDelegate((
                BuildContext context,
                int index,
              ) {
                final assetsFilteredList = getFilteredCryptos()[index];
                // final crypto = assetsFilteredList.crypto;
                // final trend = assetsFilteredList.cryptoTrendPercent;
                // final tokenBalance = assetsFilteredList.balanceCrypto;
                // final usdBalance = assetsFilteredList.balanceUsd;

                // final cryptoPrice = assetsFilteredList.cryptoPrice;
                return TokenItemWidget(token: assetsFilteredList);
                // return CoinCustomListTitle(
                //   roundedOf: roundedOf,
                //   fontSizeOf: fontSizeOf,
                //   iconSizeOf: iconSizeOf,
                //   imageSizeOf: imageSizeOf,
                //   listTitleHorizontalOf: listTitleHorizontalOf,
                //   listTitleVerticalOf: listTitleVerticalOf,
                //   trend: trend,
                //   cryptoPrice: cryptoPrice,
                //   colors: colors,
                //   crypto: crypto,
                //   currentAccount: currentAccount,
                //   isCryptoHidden: uiConfig.value.isCryptoHidden,
                //   tokenBalance: tokenBalance,
                //   usdBalance: usdBalance,
                // );
              }, childCount: getFilteredCryptos().length),
            ),
          ],
        ),
      ),
    );
  }

  // Dialog and utility methods
  void _copyToClipboard(BuildContext context, String address) {
    Clipboard.setData(ClipboardData(text: address));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Address copied to clipboard')),
    );
  }

  void _showSendDialog(BuildContext context) {
    context.push(RouterEnums.selectedToken.routeName, extra: reorganizedCrypto);
    // Implement send dialog
  }

  void _showReceiveDialog(BuildContext context) async {
    final result = await context.push(
      RouterEnums.selectedToken.routeName,
      extra: reorganizedCrypto,
    );
    if (result != null && result is Crypto) {
      // Handle the selected token
      // For example, navigate to the receive screen with the selected token
      context.push(RouterEnums.receiveToken.routeName, extra: result);
    }
  }

  void _showHistoryScreen(BuildContext context) {
    // Navigate to history screen
  }

  void _showDAppsScreen(BuildContext context) {
    // Navigate to DApps screen
    context.push(RouterEnums.discover.routeName);
  }

  void _showAddTokensDialog(BuildContext context) {
    context.push(RouterEnums.importToken.routeName);
    // showBottomSheetAddCustomToken(
    //   context,
    //   context.read<WalletCubit>().state.currentActiveChain,
    // );
  }

  void _showImportNFTDialog(BuildContext context) {
    // Implement import NFT dialog
  }
}
