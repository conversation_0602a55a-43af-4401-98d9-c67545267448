import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/screen/wallet/webview/defibrowser/defi_browser.dart';
import 'package:toii_social/screen/wallet/webview/defibrowser/js_bridge_callback_bean.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/scaffords/scafford.dart';
import 'package:web3dart/web3dart.dart';

const breakingHyphen = '-';
const nonBreakingHyphen = '\u2011';

class DappBrowerScreen extends StatefulWidget {
  final String url;
  const DappBrowerScreen({super.key, required this.url});

  @override
  State<DappBrowerScreen> createState() => _DappBrowerScreenState();
}

class _DappBrowerScreenState extends State<DappBrowerScreen> {
  // final SharedPreferencesManager sharedPreferencesManager =
  //     GetIt.instance<SharedPreferencesManager>();
  //final _bottomSheetService = GetIt.I<IBottomSheetService>();

  late final Web3Client ethClient;
  //final currentActiveChain = GetIt.instance<WalletCubit>().currentActiveChain;
  final currentActiveChain = 472025;
  var loadingPercentage = 0;
  bool isSecure = false;
  InAppWebViewGroupOptions options = InAppWebViewGroupOptions(
    crossPlatform: InAppWebViewOptions(
      useShouldOverrideUrlLoading: true,
      mediaPlaybackRequiresUserGesture: false,
    ),
    android: AndroidInAppWebViewOptions(
      useHybridComposition: true,
      domStorageEnabled: true,
      useWideViewPort: true,
      geolocationEnabled: true,
      overScrollMode: AndroidOverScrollMode.OVER_SCROLL_IF_CONTENT_SCROLLS,
    ),
    ios: IOSInAppWebViewOptions(allowsInlineMediaPlayback: true),
  );

  @override
  void initState() {
    super.initState();
    //final rpcUrl = currentActiveChain.rpcUrl ?? currentActiveChain.rpc;

    // ethClient = Web3Client(rpcUrl ?? "", http.Client());
    checkForUrlSecurity();
  }

  Widget _header() {
    return SizedBox(
      height: 68,
      child: Row(
        children: [
          Expanded(
            child: Container(
              margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
              padding: const EdgeInsetsDirectional.only(
                start: 12,
                end: 4,
                top: 12,
                bottom: 12,
              ),
              decoration: BoxDecoration(
                //  color: neutral700,
                borderRadius: const BorderRadius.all(Radius.circular(10)),
                border: Border.all(
                  //    color: ColorsTheme.of(context).screenBackground,
                ),
              ),
              child: Row(
                children: [
                  InkWell(
                    onTap: () {
                      Navigator.of(context).pop();
                    },
                    child: const Icon(
                      Icons.close_rounded,
                      //    color: white,
                      size: 24,
                    ),
                  ),

                  // InkWell(
                  //     onTap: presenter.showNetworkDetailsBottomSheet,
                  //     child: ChainLogoWidget(logo: logo)),
                  Expanded(
                    child: Row(
                      children: [
                        const SizedBox(width: 4),
                        isSecure
                            ? const Icon(
                              Icons.lock_rounded,
                              //      color: slate900,
                              size: 16,
                            )
                            : const Icon(
                              Icons.warning,
                              //     color: red500,
                              size: 16,
                            ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: InkWell(
                            // onTap: () => presenter.copyUrl(),
                            child: Text(
                              Uri.parse(widget.url).host.replaceAll(
                                breakingHyphen,
                                nonBreakingHyphen,
                              ),

                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void checkForUrlSecurity() {
    final uri = Uri.parse(widget.url);
    setState(() {
      isSecure = uri.scheme == 'https';
    });
  }

  @override
  Widget build(BuildContext context) {
    // final walletCurrent = GetIt.instance<WalletRepository>().walletCurrent();

    return BaseScaffold(
      title: Text(
        'DApp Browser',
        style: titleLarge.copyColor(themeData.neutral800),
      ),
      body: SafeArea(
        child: Column(
          children: [
            _header(),
            Expanded(
              child: Stack(
                children: [
                  DefiBrowser(
                    chainId: currentActiveChain.toString(),

                    signCallback: (params, eip1193, controller) async {
                      final id = params['id'];
                      switch (eip1193) {
                        case EIP1193.requestAccounts:
                          controller?.setAddress(
                            GetIt.instance<ProfileCubit>()
                                    .state
                                    .userModel
                                    ?.walletAddress ??
                                "",
                            id,
                          );
                        case EIP1193.signTransaction:
                          Map<String, dynamic> object = params['object'];

                          // final transaction = await _approveTransaction(object);
                          // if (transaction is Transaction) {
                          //   // Load the private key
                          //   final walletType = walletCurrent.walletType;
                          //   ValidateCardService.authen(
                          //     walletType == "card",
                          //     onResult: (p0) async {
                          //       if (p0 != null) {
                          //         final privateKey = p0.privateKeyHex;
                          //         final EthPrivateKey credentials =
                          //             EthPrivateKey.fromHex(privateKey);
                          //         final chainId = currentActiveChain.chainId;

                          //         final signature = await ethClient
                          //             .signTransaction(
                          //               credentials,
                          //               transaction,
                          //               chainId: chainId!.toInt(),
                          //             );
                          //         // Sign the transaction
                          //         final signedTx = bytesToHex(
                          //           signature,
                          //           include0x: true,
                          //         );
                          //         controller?.sendResult(signedTx, id);
                          //       }
                          //     },
                          //   );

                          //   //  response = response.copyWith(result: signedTx);
                          // } else {
                          //   controller?.cancel(id);
                          // }

                          // BridgeParams bridge = BridgeParams.fromJson(object);
                          // presenter.signTransaction(
                          //     bridge: bridge,
                          //     cancel: () {
                          //       controller?.cancel(id);
                          //     },
                          //     success: (idHash) {
                          //       controller?.sendResult(idHash, id);
                          //     },
                          //     url: url);
                          break;
                        case EIP1193.addEthereumChain:
                          controller?.cancel(id);
                          break;
                        default:
                          print("object");
                      }
                    },
                    onProgressChanged: (controller, progress) async {
                      setState(() {
                        loadingPercentage = progress;
                      });
                    },
                    walletAddress:
                        GetIt.instance<ProfileCubit>()
                            .state
                            .userModel
                            ?.walletAddress ??
                        "",
                    initialUrlRequest: URLRequest(
                      url: WebUri.uri(Uri.parse(widget.url)),
                    ),
                  ),

                  if (loadingPercentage < 100)
                    LinearProgressIndicator(value: loadingPercentage / 100.0),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Future<dynamic> _approveTransaction(Map<String, dynamic> tJson) async {
  //   Transaction transaction = tJson.toTransaction();

  //   final gasPrice = await ethClient.getGasPrice();
  //   //   try {
  //   final gasLimit = await ethClient.estimateGas(
  //     sender: transaction.from,
  //     to: transaction.to,
  //     value: transaction.value,
  //     data: transaction.data,
  //     gasPrice: gasPrice,
  //   );

  //   transaction = transaction.copyWith(
  //     gasPrice: gasPrice,
  //     maxGas: gasLimit.toInt(),
  //   );
  //   final gweiGasPrice =
  //       (transaction.gasPrice?.getInWei ?? BigInt.zero) /
  //       BigInt.from(1000000000);
  //   // final approved = await _bottomSheetService.queueBottomSheet(
  //   //   widget: WCRequestWidget(
  //   //     child: WCConnectionWidget(
  //   //       title: 'Approve Transaction',
  //   //       info: [
  //   //         WCConnectionModel(elements: [jsonEncode(tJson)]),
  //   //         WCConnectionModel(
  //   //           title: 'Gas price',
  //   //           elements: ['${gweiGasPrice.toStringAsFixed(2)} GWEI'],
  //   //         ),
  //   //       ],
  //   //     ),
  //   //   ),
  //   // );
  //   //   } on RPCError catch (e) {
  //   //     await _bottomSheetService.queueBottomSheet(
  //   //       widget: Container(
  //   //         color: Colors.white,
  //   //         height: 210.0,
  //   //         width: double.infinity,
  //   //         padding: const EdgeInsets.all(20.0),
  //   //         child: Column(
  //   //           children: [
  //   //             Icon(
  //   //               Icons.error_outline_sharp,
  //   //               color: Colors.red[100],
  //   //               size: 80.0,
  //   //             ),
  //   //             Text(
  //   //               'Error',
  //   //               style: StyleConstants.subtitleText.copyWith(
  //   //                 color: Colors.black,
  //   //                 fontSize: 18.0,
  //   //               ),
  //   //             ),
  //   //             Text(e.message),
  //   //           ],
  //   //         ),
  //   //       ),
  //   //     );

  //   //     return JsonRpcError(code: e.errorCode, message: e.message);
  //   //   }

  //   //   final gweiGasPrice = (transaction.gasPrice?.getInWei ?? BigInt.zero) /
  //   //       BigInt.from(1000000000);

  //   //   final approved = await _bottomSheetService.queueBottomSheet(
  //   //     widget: WCRequestWidget(
  //   //       child: WCConnectionWidget(
  //   //         title: 'Approve Transaction',
  //   //         info: [
  //   //           WCConnectionModel(elements: [jsonEncode(tJson)]),
  //   //           WCConnectionModel(
  //   //             title: 'Gas price',
  //   //             elements: ['${gweiGasPrice.toStringAsFixed(2)} GWEI'],
  //   //           ),
  //   //         ],
  //   //       ),
  //   //     ),
  //   //   );

  //   if (approved == true) {
  //     return transaction;
  //   }

  //   return const JsonRpcError(code: 5001, message: 'User rejected method');
  // }
}
