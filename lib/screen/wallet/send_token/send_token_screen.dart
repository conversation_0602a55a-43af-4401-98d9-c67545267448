import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/crypto/asset.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/wallet/enter_amount/enter_amount_screen.dart';
import 'package:toii_social/utils/utils.dart';
import 'package:toii_social/widget/button/button.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/scaffords/scafford.dart';

class SendTokenScreen extends StatefulWidget {
  final Asset token;
  const SendTokenScreen({super.key, required this.token});

  @override
  State<SendTokenScreen> createState() => _SendTokenScreenState();
}

class _SendTokenScreenState extends State<SendTokenScreen> {
  final TextEditingController _addressController = TextEditingController();
  final FocusNode _addressFocusNode = FocusNode();
  String _errorMessage = '';
  bool get _canContinue {
    return _addressController.text.isNotEmpty &&
        isAddress(_addressController.text);
  }

  String get validateError {
    if (_addressFocusNode.hasFocus) {
      return '';
    }
    if (_addressController.text.isEmpty) {
      return 'Please enter an address';
    }
    if (!isAddress(_addressController.text)) {
      return 'Invalid address format';
    }
    if ((GetIt.instance<ProfileCubit>().state.userModel?.walletAddress ?? "") ==
            _addressController.text &&
        _addressController.text.isNotEmpty) {
      return 'You can\'t send tokens to your own wallet';
    }
    return '';
  }

  @override
  void initState() {
    super.initState();
    _addressFocusNode.addListener(_onFocusChangeAddress);
  }

  void _onFocusChangeAddress() {
    if (_addressFocusNode.hasFocus) {
      _errorMessage = '';
    } else {
      _errorMessage = validateError;
    }
  }

  @override
  void dispose() {
    _addressController.dispose();
    _addressFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BaseScaffold(
      showLeading: true,
      title: Text(
        'Send Token',
        style: titleLarge.copyColor(themeData.neutral800),
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 12),

                  // Contact Card
                  _buildContactCard(),

                  const SizedBox(height: 14),

                  // To Label
                  Center(
                    child: Text(
                      'To',
                      style: labelLarge.copyColor(themeData.neutral800),
                    ),
                  ),

                  const SizedBox(height: 14),

                  // Address Input
                  _buildAddressInput(),
                ],
              ),
            ),
          ),

          // Continue Button
          _buildContinueButton(),
        ],
      ),
    );
  }

  Widget _buildContactCard() {
    final address =
        GetIt.instance<ProfileCubit>().state.userModel?.walletAddress ?? "";
    final userName =
        GetIt.instance<ProfileCubit>().state.userModel?.username ?? "User";

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: themeData.primaryGreen50,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          // Avatar
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color: themeData.neutral300,
              shape: BoxShape.circle,
            ),
            child: Icon(Icons.person, color: themeData.neutral800, size: 20),
          ),

          const SizedBox(width: 12),

          // Contact Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  userName,
                  style: titleMedium.copyColor(themeData.neutral800),
                ),
                const SizedBox(height: 4),
                Text(
                  shortenAddress(address),
                  style: labelLarge.copyColor(themeData.neutral400),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddressInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color:
                  _addressFocusNode.hasFocus
                      ? themeData.primaryGreen500
                      : _errorMessage.isNotEmpty
                      ? themeData.red500
                      : themeData.neutral200,
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _addressController,
                  focusNode: _addressFocusNode,
                  style: bodyMedium.copyColor(
                    !_addressFocusNode.hasFocus && _errorMessage.isNotEmpty
                        ? themeData.red500
                        : themeData.neutral800,
                  ),
                  decoration: InputDecoration(
                    hintText: 'Enter Address',
                    hintStyle: bodyMedium.copyColor(themeData.neutral400),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.all(16),
                  ),
                  onChanged: (value) {
                    setState(() {
                      _errorMessage = validateError;
                    });
                  },
                ),
              ),

              // QR Scanner Button
              GestureDetector(
                onTap: _showQRScanner,
                child: SizedBox(
                  width: 40,
                  height: 40,
                  child: Center(
                    child: SvgPicture.asset(
                      Assets.icons.icScanQr.path,
                      width: 24,
                      height: 24,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 6),
        Text(_errorMessage, style: bodySmall.copyColor(themeData.red500)),
        const SizedBox(height: 6),
      ],
    );
  }

  Widget _buildContinueButton() {
    return Container(
      padding: EdgeInsets.only(
        left: 16,
        right: 16,
        top: 16,
        bottom: MediaQuery.of(context).padding.bottom + 16,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: themeData.neutral200)),
      ),
      child: Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: TSButton.primary(
              isEnabled: _canContinue,
              title: 'Continue',
              onPressed: () {
                if (_canContinue) {
                  _continueSend();
                } else {}
              },
            ),
          ),
        ],
      ),
    );
  }

  void _showQRScanner() async {
    final result = await context.push(
      RouterEnums.scanQr.routeName,
      extra: {
        'onScan': (String scannedAddress) {
          _addressController.text = scannedAddress;
          setState(() {});
        },
      },
    );

    // If the result is not null, it means the QR scan was successful
    if (result != null && result is String) {
      _addressController.text = result;
      setState(() {});
    }
  }

  void _continueSend() {
    // Navigate to amount selection screen
    context.push(
      RouterEnums.enterAmount.routeName,
      extra: EnterAmountArgs(widget.token, _addressController.text),
    );
  }
}
