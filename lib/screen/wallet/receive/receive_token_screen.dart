import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get_it/get_it.dart';
import 'package:pretty_qr_code/pretty_qr_code.dart';
import 'package:share_plus/share_plus.dart';
import 'package:toii_social/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/crypto/crypto.dart';
import 'package:toii_social/utils/utils.dart';
import 'package:toii_social/widget/button/button.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/scaffords/scafford.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';

class ReceiveTokenScreen extends StatelessWidget {
  final Crypto? token;
  const ReceiveTokenScreen({super.key, this.token});

  @override
  Widget build(BuildContext context) {
    final address =
        GetIt.instance<ProfileCubit>().state.userModel?.walletAddress ?? "";
    final userName =
        GetIt.instance<ProfileCubit>().state.userModel?.username ?? "User";

    return BaseScaffold(
      //  showLeading: true,
      //leadingIcon: Icons.close,
      title: Column(
        children: [
          Text("Receive", style: titleLarge.copyColor(themeData.neutral800)),
          Text(
            token?.symbol?.toUpperCase() ?? "",
            style: bodyMedium.copyColor(themeData.neutral400),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          children: [
            Container(
              height: 60,
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(10.0),
            //    color: warningColor.withOpacity(0.15),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
            //      Icon(LucideIcons.octagonAlert, color: warningColor),
                  SizedBox(width: 5),
                  // Expanded(
                  //   child: RichText(
                  //     text: TextSpan(
                  //       text: "Only send ",
                  //       // style: textTheme.bodyMedium?.copyWith(
                  //       //   color: warningColor,
                  //       // ),
                  //       children: [
                  //         TextSpan(
                  //           text: crypto!.name,
                  //           style: textTheme.bodyMedium?.copyWith(
                  //             fontSize: fontSizeOf(12),
                  //             color: warningColor,
                  //             fontWeight: FontWeight.bold,
                  //           ),
                  //         ),
                  //         TextSpan(
                  //           text:
                  //               " assets to this address , other assets will be lost forever.",
                  //           style: textTheme.bodyMedium?.copyWith(
                  //             color: warningColor,
                  //             fontSize: fontSizeOf(12),
                  //           ),
                  //         ),
                  //       ],
                  //     ),
                  //     overflow: TextOverflow.clip,
                  //   ),
                  // ),
                ],
              ),
            ),
            const SizedBox(height: 36),

            // Token Icon and Name
            if (token != null)
              Container(
                width: 54,
                height: 54,
                decoration: const BoxDecoration(
                  color: Color(0xFF8BC34A),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Text(
                    token?.symbol?[0] ?? 'T',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),

            if (token != null) const SizedBox(height: 16),

            if (token != null)
              Text(
                token?.symbol?.toUpperCase() ?? "TOII",
                style: titleLarge.copyColor(themeData.neutral800),
              ),

            const SizedBox(height: 40),

            // QR Code
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Container(
                width: 242,
                height: 242,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                ),
                child: PrettyQr(
                  data: address,
                  size: 200,
                  elementColor: Colors.black,
                  errorCorrectLevel: QrErrorCorrectLevel.M,
                  roundEdges: true,
                ),
              ),
            ),

            const Spacer(),

            // User Info Section
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: themeData.neutral100,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  // Profile Avatar
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: themeData.neutral300,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.person,
                      //    color: themeData.neutral600,
                      size: 20,
                    ),
                  ),

                  const SizedBox(width: 12),

                  // User Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          userName,
                          style: bodyMedium.copyColor(themeData.neutral800),
                        ),
                        Text(
                          shortenAddress(address),
                          style: bodySmall.copyColor(themeData.neutral400),
                        ),
                      ],
                    ),
                  ),

                  // Copy Button
                  GestureDetector(
                    onTap: () async {
                      await Clipboard.setData(ClipboardData(text: address));
                      // ignore: use_build_context_synchronously
                      context.showSnackbar(message: "Copied to clipboard");
                    },
                    child: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Center(
                        child: SvgPicture.asset(
                          Assets.icons.icCopy.path,
                          colorFilter: ColorFilter.mode(
                            themeData.neutral800,
                            BlendMode.srcIn,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // Share Button
            SizedBox(
              width: double.infinity,
              child: TSButton.primary(
                title: "Share",
                onPressed: () {
                  Share.share(address);
                },
              ),
            ),

            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }
}
