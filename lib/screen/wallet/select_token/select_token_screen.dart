import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/crypto/asset.dart';
import 'package:toii_social/screen/wallet/widgets/crypto_logo_network.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/scaffords/scafford.dart';

class SelectTokenScreen extends StatefulHookConsumerWidget {
  final List<Asset> initialCryptos;
  const SelectTokenScreen({super.key, this.initialCryptos = const []});

  @override
  ConsumerState<SelectTokenScreen> createState() => _SelectTokenScreenState();
}

class _SelectTokenScreenState extends ConsumerState<SelectTokenScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<Asset> _filteredTokens = [];
  List<Asset> initialCryptos = [];
  @override
  void initState() {
    super.initState();
    initialCryptos = widget.initialCryptos.toList();
    _filteredTokens = widget.initialCryptos.toList();
    _searchController.addListener(_filterTokens);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterTokens() {
    setState(() {
      _filteredTokens =
          initialCryptos.where((token) {
            return token.crypto.name.toLowerCase().contains(
                  _searchController.text.toLowerCase(),
                ) ||
                token.crypto.symbol.toLowerCase().contains(
                  _searchController.text.toLowerCase(),
                );
          }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return BaseScaffold(
      showLeading: true,
      title: Text(
        'Select Tokens',
        style: titleLarge.copyColor(themeData.neutral800),
      ),

      body: Column(
        children: [
          const SizedBox(height: 16),
          // Search bar
          _buildSearchBar(context),

          const SizedBox(height: 16),

          // Token list
          Expanded(
            child: ListView.separated(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: _filteredTokens.length,
              separatorBuilder:
                  (context, index) => Container(
                    height: 1,
                    color: themeData.neutral200,
                    margin: const EdgeInsets.symmetric(vertical: 8),
                  ),
              itemBuilder: (context, index) {
                return _buildTokenItem(context, _filteredTokens[index]);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: themeData.black50,
        borderRadius: BorderRadius.circular(100),
      ),
      child: Row(
        children: [
          SvgPicture.asset(
            Assets.icons.icSearch.path,
            colorFilter: ColorFilter.mode(
              themeData.neutral400,
              BlendMode.srcIn,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: TextField(
              controller: _searchController,
              style: bodyMedium.copyWith(color: themeData.textPrimary),
              decoration: InputDecoration(
                hintText: 'Search token symbol',
                hintStyle: bodyMedium.copyWith(color: themeData.textSecondary),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(vertical: 12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTokenItem(BuildContext context, Asset token) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: () {
        Navigator.of(context).pop(token);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            // Token icon
            CryptoLogoNetwork(
              size: 40,
              crypto: token.crypto,
              id: token.crypto.idLogo.toString(),
            ),
            const SizedBox(width: 12),

            // Token info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    token.crypto.name,
                    style: bodyMedium.copyWith(
                      color: theme.textPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    token.crypto.symbol,
                    style: bodySmall.copyWith(color: theme.textSecondary),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
