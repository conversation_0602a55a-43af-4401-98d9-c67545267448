import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/core/repository/chain_repository.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/model/chain/token_item_model.dart';
import 'package:toii_social/model/crypto/asset.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/wallet/confirm_send/confirm_send_screen.dart';
import 'package:toii_social/screen/wallet/widgets/crypto_logo_network.dart';
import 'package:toii_social/widget/button/button.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/scaffords/scafford.dart';
import 'package:web3dart/web3dart.dart';

class EnterAmountArgs {
  final Asset token;
  final String address;
  EnterAmountArgs(this.token, this.address);
}

class EnterAmountScreen extends StatefulWidget {
  final EnterAmountArgs arg;

  const EnterAmountScreen({super.key, required this.arg});

  @override
  State<EnterAmountScreen> createState() => _EnterAmountScreenState();
}

class _EnterAmountScreenState extends State<EnterAmountScreen> {
  final TextEditingController _amountController = TextEditingController();
  final FocusNode _amountFocusNode = FocusNode();
  late final double _usdRate;
  String gasFee = "";
  bool isFetchingGasFee = false;
  bool get _canContinue =>
      (gasFee.isNotEmpty && !isFetchingGasFee) &&
      _amountController.text.isNotEmpty &&
      amount <=
          ((widget.arg.token.balanceCrypto ?? 0) - (double.tryParse(gasFee) ?? 0));

  String get _usdValue {
    if (_amountController.text.isEmpty) return '\$0.00';
    final usd = amount * _usdRate;
    return '= \$${usd.toStringAsFixed(2)}';
  }

  @override
  void initState() {
    super.initState();
    _usdRate = widget.arg.token.balanceUsd ?? 0;
    _amountController.addListener(() {
      setState(() {});
    });
    // Auto-focus the text field to show keyboard
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _amountFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _amountController.dispose();
    _amountFocusNode.dispose();
    super.dispose();
  }

  double get amount {
    final text = _amountController.text.replaceAll(",", ".");
    if (text.isEmpty) return 0;
    return double.tryParse(text) ?? 0;
  }

  void fetchGasFee() async {
    if (amount <= 0) {
      setState(() {
        gasFee = "";
      });
      return;
    }
    setState(() {
      isFetchingGasFee = true;
      gasFee = "";
    });

    // Transaction Desc
    String destinationAddress = widget.arg.address;
    // EtherAmount? amount = EtherAmount.fromBigInt(
    //   EtherUnit.wei,
    //   BigInt.from(
    //     double.parse(
    //           _amountController.text == "" ? "0" : _amountController.text,
    //         ) *
    //         pow(10, 18),
    //   ),
    // );

    // Fetch Gas Fee
    // EtherAmount gasEstimation = selectedTokenContract == "NATIVE"
    EtherAmount gasEstimation = await GetIt.instance<ChainRepository>()
        .fetchEstimateGasFee(
          receipentPublicAddress: destinationAddress,
          crypto: widget.arg.token.crypto
        );
    // : await ChainRepository.fetchEstimateTokenTransferGasFee(
    //     senderPublicAddress:
    //         WalletController.to.currentActiveWallet.publicAddress!,
    //     receipentPublicAddress: destinationAddress,
    //     contractAddress: selectedTokenContract,
    //     chain: WalletController.to.currentActiveChain,
    //     value: amount,
    //   );

    setState(() {
      isFetchingGasFee = false;
      gasFee =
          gasEstimation
              .getValueInUnit(EtherUnit.ether)
              .toStringAsFixed(8)
              .toString();
    });
  }

  @override
  Widget build(BuildContext context) {
    return BaseScaffold(
      showLeading: true,
      title: Text(
        'Enter Amount',
        style: titleLarge.copyColor(themeData.neutral800),
      ),

      body: Column(
        children: [
          const SizedBox(height: 16),

          Expanded(
            child: Column(
              children: [
                // Amount Display
                _buildAmountDisplay(context),
              ],
            ),
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "Gas Fees (${widget.arg.token.crypto.symbol?.toUpperCase() ?? ""})",
                style: TextStyle(
                  color: Color(0xFF141414).withOpacity(0.4),
                  fontSize: 15,
                  fontWeight: FontWeight.w600,
                ),
              ),
              isFetchingGasFee
                  ? const SizedBox(
                    width: 25,
                    height: 25,
                    child: CircularProgressIndicator(),
                  )
                  : Text(
                    gasFee,
                    style: const TextStyle(
                      color: Color(0xFF141414),
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
            ],
          ),
          // Balance Section
          _buildBalanceSection(context),
          const SizedBox(height: 16),

          // Continue Button
          _buildContinueButton(context),

          const SizedBox(height: 24),
        ],
      ),
    );
  }

  Widget _buildAmountDisplay(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      children: [
        // Hidden text field for input
        Opacity(
          opacity: 0.01, // Nearly invisible but still functional
          child: TextField(
            maxLength: 16,
            controller: _amountController,
            focusNode: _amountFocusNode,
            keyboardType: TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [decimalInputFormatter(9)],
            style: const TextStyle(color: Colors.transparent),
            decoration: const InputDecoration(border: InputBorder.none),
            showCursor: false,
            onChanged: (value) {
              fetchGasFee();
            },
          ),
        ),

        // Visual amount display
        GestureDetector(
          onTap: () => _amountFocusNode.requestFocus(),
          child: Column(
            children: [
              // Cursor or amount
              if (_amountController.text.isEmpty) ...[
                Container(width: 2, height: 40, color: theme.primaryGreen500),
              ] else ...[
                Text(
                  '${_amountController.text} ${widget.arg.token.crypto.symbol?.toUpperCase() ?? ""}',
                  style: displayMedium.copyWith(
                    color: theme.textPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  _usdValue,
                  style: bodyLarge.copyWith(color: theme.textSecondary),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBalanceSection(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.neutral100,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          // Token Icon
          CryptoLogoNetwork(
            id: widget.arg.token.crypto.idLogo.toString(),
            crypto: widget.arg.token.crypto,
            size: 32,
          ),

          const SizedBox(width: 12),

          // Balance Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Balance',
                  style: titleMedium.copyWith(color: themeData.neutral400),
                ),
                Text(
                  '${widget.arg.token.balanceCrypto} ${widget.arg.token.crypto.symbol?.toUpperCase() ?? ""}',
                  style: titleMedium.copyColor(themeData.neutral800),
                ),
              ],
            ),
          ),

          // Max Button
          GestureDetector(
            onTap: () => _setMaxAmount(),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                border: Border.all(color: theme.primaryGreen500),
                borderRadius: BorderRadius.circular(6),
              ),
              child: Text(
                'Max',
                style: bodySmall.copyWith(
                  color: theme.primaryGreen500,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContinueButton(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      width: double.infinity,
      child: TSButton.primary(
        isEnabled: _canContinue,
        title: 'Continue',
        onPressed: () {
          if (_canContinue) {
            _continue(context);
          }
        },
      ),
    );
  }

  void _setMaxAmount() {
    _amountController.text = widget.arg.token.balanceCrypto.toString();
    _amountController.selection = TextSelection.fromPosition(
      TextPosition(offset: _amountController.text.length),
    );
    fetchGasFee();
  }

  void _continue(BuildContext context) {
    // Navigate to confirmation screen
    final arg = ConfirmSendScreenArgs(
      networkFee: gasFee,
      token: widget.arg.token,
      address: widget.arg.address,
      amount: _amountController.text,
    );

    context.push(RouterEnums.confirmSendToken.routeName, extra: arg);
  }
}

class DecimalTextInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final text = newValue.text;

    // Allow empty string
    if (text.isEmpty) return newValue;

    // Allow only digits and one optional decimal point
    final regex = RegExp(r'^\d*\.?\d*$');
    if (!regex.hasMatch(text)) return oldValue;

    // Prevent multiple decimal points
    if ('.'.allMatches(text).length > 1) return oldValue;

    return newValue;
  }
}

TextInputFormatter decimalInputFormatter(int decimals) {
  return RegExInputFormatter.withRegex(
    '^[0-9]{0,}([\\.\\,][0-9]{0,$decimals})?\$',
  );
}

class RegExInputFormatter implements TextInputFormatter {
  factory RegExInputFormatter.withRegex(String regexString) {
    final regex = RegExp(regexString);
    return RegExInputFormatter._(regex);
  }

  RegExInputFormatter._(this._regExp);
  final RegExp _regExp;

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final oldValueValid = _isValid(oldValue.text);
    final newValueValid = _isValid(newValue.text);
    if (oldValueValid && !newValueValid) {
      return oldValue;
    }
    return newValue;
  }

  bool _isValid(String value) {
    try {
      final matches = _regExp.allMatches(value);
      for (final Match match in matches) {
        if (match.start == 0 && match.end == value.length) {
          return true;
        }
      }
      return false;
    } catch (e) {
      // Invalid regex
      assert(false, e.toString());
      return true;
    }
  }
}
