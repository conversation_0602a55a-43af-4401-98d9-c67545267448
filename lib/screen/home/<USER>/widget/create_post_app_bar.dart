import 'package:flutter/material.dart';
import 'package:toii_social/widget/button/button.dart';

class CreatePostAppBar extends StatelessWidget {
  final VoidCallback onClose;
  final VoidCallback onPost;
  final bool isLoading;
  final bool isEnabled;
  final bool isUpdate;
  const CreatePostAppBar({
    super.key,
    required this.onClose,
    required this.onPost,
    this.isLoading = false,
    this.isEnabled = true,
    this.isUpdate = false,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 64,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          InkWell(
            borderRadius: BorderRadius.circular(12),
            onTap: onClose,
            child: Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: Theme.of(
                  context,
                ).colorScheme.onSurface.withOpacity(0.05),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.close,
                size: 20,
                color: Color(0xFF292929),
              ),
            ),
          ),
          const SizedBox(width: 16),
          const Spacer(),
          TSButton(
            title: isUpdate ? "Update" : "Post",
            size: ButtonSize.small,
            isLoading: isLoading,
            elevatedVariant: ButtonVariant.primary,
            isEnabled: true,
            onPressed: onPost,
          ),
        ],
      ),
    );
  }
}
