import 'package:flutter/material.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/screen/home/<USER>/enum/privacy_option_enum.dart';
import 'package:toii_social/widget/colors/colors.dart';

class CreatePostActionBar extends StatelessWidget {
  final VoidCallback onImage;
  final VoidCallback onPrivacy;
  final PrivacyOption privacy;
  const CreatePostActionBar({
    super.key,
    required this.onImage,
    required this.onPrivacy,
    required this.privacy,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 56,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: context.themeData.neutral100, width: 1),
        ),
      ),
      child: Row(
        children: [
          Row(
            children: [
              GestureDetector(
                onTap: onImage,
                child: Assets.icons.icPicture.svg(
                  width: 24,
                  height: 24,
                  color: context.themeData.neutral800,
                ),
              ),
              const SizedBox(width: 12),
              Assets.icons.icMusic.svg(
                width: 24,
                height: 24,
                color: context.themeData.neutral800,
              ),
              const SizedBox(width: 12),
              // Assets.icons.biEmojiSmile.svg(
              //   width: 24,
              //   height: 24,
              //   color: context.themeData.neutral800,
              // ),
            ],
          ),
          const Spacer(),
          GestureDetector(onTap: onPrivacy, child: privacy.icon),
          const SizedBox(width: 12),
        ],
      ),
    );
  }
}
