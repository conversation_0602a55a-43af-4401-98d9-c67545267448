import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/cubit/post/home/<USER>';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/screen/home/<USER>/home_appbar_widget.dart';
import 'package:toii_social/screen/home/<USER>/home_following_widget.dart';
import 'package:toii_social/screen/home/<USER>/home_item_post_widget.dart';
import 'package:toii_social/screen/home/<USER>/home_skeleton_widget.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/pull_refresh/app_refresh_view.dart';

class HomeMainPage extends StatefulWidget {
  final ScrollController scrollController;
  const HomeMainPage({super.key, required this.scrollController});

  @override
  State<HomeMainPage> createState() => HomeMainPageState();
}

class HomeMainPageState extends State<HomeMainPage>
    with AutomaticKeepAliveClientMixin, TickerProviderStateMixin {
  final _homeCubit = GetIt.instance<HomeCubit>();
  late TabController _tabController;
  static const double _endReachedThreshold = 200;
  final List<String> tabs = const ['Now', 'Following'];
  int indexTab = 0;
  @override
  void initState() {
    super.initState();
    widget.scrollController.addListener(_onScroll);

    _tabController = TabController(length: 2, vsync: this);
  }

  void _onScroll() {
    if (!widget.scrollController.hasClients) {
      return;
    }

    final thresholdReached =
        widget.scrollController.position.extentAfter <
        _endReachedThreshold; // Check xem đã đạt tới _endReachedThreshold chưa

    if (thresholdReached) {
      if (indexTab == 0) {
        // "Now" tab - load more main feed
        if (_homeCubit.state.status != HomeStatus.loading &&
            _homeCubit.state.canLoadMore) {
          _homeCubit.getUserFeed();
        }
      } else if (indexTab == 1) {
        // "Following" tab - load more following feed
        if (_homeCubit.state.followingStatus != FollowingStatus.loading &&
            _homeCubit.state.canLoadMoreFollowing) {
          _homeCubit.getUserFeedFollowing();
        }
      }
    }
  }

  void scrollToTop() {
    print("Scroll to top -------------------");
    widget.scrollController.animateTo(
      widget.scrollController.position.minScrollExtent,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeOut,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  Widget _buildCustomTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      height: 36,
      child: ListView.separated(
        physics: const NeverScrollableScrollPhysics(),
        scrollDirection: Axis.horizontal,
        itemBuilder: (context, index) {
          return GestureDetector(
            onTap: () {
              setState(() {
                indexTab = index;
              });

              // Load following feed when Following tab is selected
              if (index == 1 && _homeCubit.state.followingPosts.isEmpty) {
                _homeCubit.getUserFeedFollowing();
              }
            },
            child: Container(
              alignment: Alignment.center,
              padding: const EdgeInsets.symmetric(horizontal: 16),

              child: IntrinsicWidth(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      tabs[index],
                      style: titleLarge.copyColor(
                        indexTab == index
                            ? themeData.neutral800
                            : themeData.neutral400,
                      ),
                    ),
                    SizedBox(
                      height: 4,
                      child: Row(
                        children: [
                          Expanded(
                            child: Container(
                              decoration: BoxDecoration(
                                color:
                                    indexTab == index
                                        ? themeData.primaryGreen500
                                        : Colors.transparent,
                              ),
                            ),
                          ),
                          Expanded(child: const SizedBox()),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
        separatorBuilder: (context, index) {
          return const SizedBox();
        },
        itemCount: tabs.length,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return MultiBlocProvider(
      providers: [BlocProvider(create: (_) => _homeCubit..getUserFeed())],
      child: Scaffold(
        backgroundColor: Colors.transparent,
        extendBodyBehindAppBar: true,
        body: Container(
          height: double.infinity,
          width: double.infinity,
          decoration: BoxDecoration(
            image: DecorationImage(
              image: Assets.images.bgHome.provider(),
              fit: BoxFit.cover,
            ),
          ),
          child: BlocBuilder<HomeCubit, HomeState>(
            builder: (context, state) {
              return SafeArea(
                bottom: false,
                child: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: scrollToTop,
                  child: PullToRefreshView(
                    onRefresh: () async {
                      if (indexTab == 0) {
                        context.read<HomeCubit>().getUserFeed(isRefresh: true);
                      } else if (indexTab == 1) {
                        context.read<HomeCubit>().getUserFeedFollowing(
                          isRefresh: true,
                        );
                      }
                    },
                    child: CustomScrollView(
                      controller: widget.scrollController,
                      slivers: [
                        SliverLayoutBuilder(
                          builder: (BuildContext context, constraints) {
                            final scrolled = constraints.scrollOffset > 0;

                            return SliverAppBar(
                              elevation: 0,
                              scrolledUnderElevation: 0,
                              backgroundColor:
                                  scrolled
                                      ? themeData.neutral50.withOpacity(0.95)
                                      : Colors.transparent,
                              shadowColor: Colors.transparent,
                              pinned: false,
                              snap: true,
                              automaticallyImplyLeading: false,
                              floating: true,
                              //      expandedHeight: 52,
                              centerTitle: false,
                              flexibleSpace: GestureDetector(
                                onTap: scrollToTop,
                                child: spaceBar(scrolled),
                              ),
                            );
                          },
                        ),
                        SliverToBoxAdapter(child: _buildCustomTabBar()),
                        // Show content based on selected tab
                        if (indexTab == 0) ...[
                          // "Now" tab content - existing posts list
                          if (state.status.isLoading && state.posts.isEmpty)
                            const HomeSkeletonListWidget(itemCount: 5)
                          else
                            SliverList(
                              delegate: SliverChildBuilderDelegate((
                                context,
                                index,
                              ) {
                                if (index < state.posts.length) {
                                  return HomeItemPostWidget(
                                    post: state.posts[index],
                                    isShowActionMore: true,
                                    isNotOnTap: false,
                                  );
                                } else {
                                  return const SizedBox.shrink();
                                }
                              }, childCount: state.posts.length),
                            ),
                          SliverToBoxAdapter(
                            child:
                                state.canLoadMore
                                    ? Container(
                                      height: 64,
                                      margin: EdgeInsets.only(bottom: 16),
                                      alignment: Alignment.center,
                                      child: Center(
                                        child: CircularProgressIndicator(),
                                      ),
                                    )
                                    : SizedBox(),
                          ),
                        ] else ...[
                          // "Following" tab content - new widget
                          const HomeFollowingWidget(),
                          SliverToBoxAdapter(
                            child:
                                state.canLoadMoreFollowing
                                    ? Container(
                                      height: 64,
                                      margin: EdgeInsets.only(bottom: 16),
                                      alignment: Alignment.center,
                                      child: Center(
                                        child: CircularProgressIndicator(),
                                      ),
                                    )
                                    : SizedBox(),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }
}
