import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_it/get_it.dart';
import 'package:toii_social/cubit/post/home/<USER>';
import 'package:toii_social/cubit/post/repost/repost_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/screen/home/<USER>/bottom_sheet_repost.dart';
import 'package:toii_social/widget/bottom_sheet/tt_bottom_sheet.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';

class PostRepostButton extends StatelessWidget {
  const PostRepostButton({super.key, required this.post});

  final PostModel post;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => RepostCubit(),
      child: BlocListener<RepostCubit, RepostState>(
        listener: (context, state) {
          if (state.status == RepostStatus.success) {
            context.showSnackbar(message: "Repost successfully");
            GetIt.instance<HomeCubit>().getUserFeed(isRefresh: true);
          } else if (state.status == RepostStatus.failure) {
            context.showSnackbar(message: state.errorMessage ?? "");
          }
        },
        child: Builder(
          builder: (context) {
            return GestureDetector(
              onTap: () {
                showTtBottomSheet(
                  context,
                  child: BottomSheetRepost(
                    onRepost: () {
                      context.read<RepostCubit>().repost(
                        originalPostId: post.id,
                      );
                    },
                    onQuote: () {
                      // TODO: Navigate to quote repost screen
                      context.showSnackbar(
                        message: "Tính năng Quote sẽ được phát triển sau",
                      );
                    },
                  ),
                );
              },
              child: Assets.icons.icRepost.svg(
                width: 20,
                height: 20,
                colorFilter: ColorFilter.mode(
                  Colors.white.withValues(alpha: 0.9),
                  BlendMode.srcIn,
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
