import 'package:blur/blur.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/home/<USER>/home_details_screen.dart';
import 'package:toii_social/screen/home/<USER>/post/post_icon_text.dart';
import 'package:toii_social/screen/home/<USER>/post/post_like_button.dart';
import 'package:toii_social/screen/home/<USER>/post/post_repost_button.dart';
import 'package:toii_social/screen/home/<USER>/post/post_share_button.dart';

class PostActionBar extends StatelessWidget {
  final PostModel post;
  final bool? isHideRepost;
  const PostActionBar({super.key, required this.post, this.isHideRepost});

  @override
  Widget build(BuildContext context) {
    //    Image.asset('assets/cat.png').blurred(
    //    colorOpacity: 0.2,
    //    borderRadius: BorderRadius.horizontal(right: Radius.circular(20)),
    //    blur: blurValue,
    //    overlay: Text(
    //      'Cat',
    //      style: theme.textTheme.headline2!.copyWith(color: theme.scaffoldBackgroundColor),
    //    ),
    //  )
    return SizedBox(
      width: 256,
      height: 40,
      child: Container(height: 40).blurred(
        colorOpacity: 0.8,
        blurColor: Colors.black,
        borderRadius: BorderRadius.all(Radius.circular(8)),
        blur: 4,
        overlay: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Expanded(
              child: Center(
                child: PostLikeButton(post: post, initialLiked: post.isLiked),
              ),
            ),

            Expanded(
              child: Center(
                child: PostIconText(
                  icon: Assets.icons.icResponses.svg(width: 20, height: 20),
                  text: post.getViewComment,
                  onTap: () {
                    context.push(
                      RouterEnums.homeDetails.routeName,
                      extra: HomeDetailsArguments(
                        postModel: post,
                        isForcusComment: true,
                      ),
                    );
                  },
                ),
              ),
            ),
            // if (!(isHideRepost == true))
            Expanded(child: Center(child: PostRepostButton(post: post))),

            Expanded(child: Center(child: PostShareButton(post: post))),
          ],
        ),
      ),
    );
  }
}
