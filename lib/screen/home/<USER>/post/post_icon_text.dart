import 'package:flutter/material.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class PostIconText extends StatelessWidget {
  final Widget icon;
  final String text;
  final VoidCallback? onTap;
  const PostIconText({
    super.key,
    required this.icon,
    required this.text,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        onTap?.call();
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          icon,
          const SizedBox(width: 4),
          Text(text, style: bodySmall.copyWith(color: Colors.white)),
        ],
      ),
    );
  }
}
