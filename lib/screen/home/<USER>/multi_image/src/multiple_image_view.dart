import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/model/post/post_model.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/home/<USER>/detail_preview_img.dart';

import 'smart_image.dart';

class MultipleImageView extends StatelessWidget {
  final List<String> imageUrls;
  final PostModel? post;

  const MultipleImageView({super.key, required this.imageUrls, this.post});

  void _onImageTap(BuildContext context, int index) {
    if (post != null) {
      context.push(
        RouterEnums.detailPreviewImg.routeName,
        extra: DetailPreviewImgArg(
          images: imageUrls,
          initialIndex: index,
          post: post,
        ),
      );
    }
  }

  Widget _wrapWithHeroAndGesture(
    Widget child,
    int index,
    BuildContext context,
  ) {
    return GestureDetector(
      onTap: () => _onImageTap(context, index),
      child: Hero(
        tag: '${post?.id ?? 'multi'}_${imageUrls[index]}_$index',
        child: child,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: _contentRender(context),
    );
  }

  List<Widget> _contentRender(BuildContext context) {
    switch (imageUrls.length) {
      case 1:
        return _singleImageView(context);

      case 2:
        return _twoImageView(context);

      case 3:
        return _threeImageView(context);

      case 4:
        return _foureImageView(context);

      default:
        return _multipleImageView(context);
    }
  }

  List<Widget> _singleImageView(BuildContext context) {
    return [
      Expanded(
        flex: 1,
        child: _wrapWithHeroAndGesture(
          ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: SmartImage(imageUrls.first, fit: BoxFit.cover, isPost: true),
          ),
          0,
          context,
        ),
      ),
    ];
  }

  List<Widget> _twoImageView(BuildContext context) {
    return [
      Expanded(
        flex: 1,
        child: _wrapWithHeroAndGesture(
          ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              bottomLeft: Radius.circular(20),
            ),
            child: SmartImage(imageUrls.first, fit: BoxFit.cover, isPost: true),
          ),
          0,
          context,
        ),
      ),
      const SizedBox(width: 5),
      Expanded(
        flex: 1,
        child: _wrapWithHeroAndGesture(
          ClipRRect(
            borderRadius: const BorderRadius.only(
              topRight: Radius.circular(20),
              bottomRight: Radius.circular(20),
            ),
            child: SmartImage(imageUrls.last, fit: BoxFit.cover, isPost: true),
          ),
          1,
          context,
        ),
      ),
    ];
  }

  List<Widget> _threeImageView(BuildContext context) {
    return [
      Expanded(
        flex: 1,
        child: _wrapWithHeroAndGesture(
          ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              bottomLeft: Radius.circular(20),
            ),
            child: SmartImage(imageUrls[0], fit: BoxFit.cover, isPost: true),
          ),
          0,
          context,
        ),
      ),
      const SizedBox(width: 5),
      Expanded(
        flex: 1,
        child: Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              flex: 1,
              child: _wrapWithHeroAndGesture(
                ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(20),
                  ),
                  child: SmartImage(
                    imageUrls[1],
                    fit: BoxFit.cover,
                    isPost: true,
                  ),
                ),
                1,
                context,
              ),
            ),
            const SizedBox(height: 5),
            Expanded(
              flex: 1,
              child: _wrapWithHeroAndGesture(
                ClipRRect(
                  borderRadius: const BorderRadius.only(
                    bottomRight: Radius.circular(20),
                  ),
                  child: SmartImage(
                    imageUrls[2],
                    fit: BoxFit.cover,
                    isPost: true,
                  ),
                ),
                2,
                context,
              ),
            ),
          ],
        ),
      ),
    ];
  }

  List<Widget> _foureImageView(BuildContext context) {
    return [
      Expanded(
        flex: 2,
        child: _wrapWithHeroAndGesture(
          ClipRRect(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              bottomLeft: Radius.circular(20),
            ),
            child: SmartImage(imageUrls[0], fit: BoxFit.cover, isPost: true),
          ),
          0,
          context,
        ),
      ),
      const SizedBox(width: 5),
      Expanded(
        flex: 1,
        child: Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              flex: 1,
              child: _wrapWithHeroAndGesture(
                ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(20),
                  ),
                  child: SmartImage(
                    imageUrls[1],
                    fit: BoxFit.cover,
                    isPost: true,
                  ),
                ),
                1,
                context,
              ),
            ),
            const SizedBox(height: 5),
            Expanded(
              flex: 1,
              child: _wrapWithHeroAndGesture(
                ClipRRect(
                  child: SmartImage(
                    imageUrls[2],
                    fit: BoxFit.cover,
                    isPost: true,
                  ),
                ),
                2,
                context,
              ),
            ),
            const SizedBox(height: 5),
            Expanded(
              flex: 1,
              child: _wrapWithHeroAndGesture(
                ClipRRect(
                  borderRadius: const BorderRadius.only(
                    bottomRight: Radius.circular(20),
                  ),
                  child: SmartImage(
                    imageUrls[3],
                    fit: BoxFit.cover,
                    isPost: true,
                  ),
                ),
                3,
                context,
              ),
            ),
          ],
        ),
      ),
    ];
  }

  List<Widget> _multipleImageView(BuildContext context) {
    return [
      Expanded(
        flex: 2,
        child: _wrapWithHeroAndGesture(
          ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20),
              bottomLeft: Radius.circular(20),
            ),
            child: SmartImage(imageUrls[0], fit: BoxFit.cover, isPost: true),
          ),
          0,
          context,
        ),
      ),
      const SizedBox(width: 5),
      Expanded(
        flex: 1,
        child: Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              flex: 1,
              child: _wrapWithHeroAndGesture(
                ClipRRect(
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(20),
                  ),
                  child: SmartImage(
                    imageUrls[1],
                    fit: BoxFit.cover,
                    isPost: true,
                  ),
                ),
                1,
                context,
              ),
            ),
            const SizedBox(height: 5),
            Expanded(
              flex: 1,
              child: _wrapWithHeroAndGesture(
                ClipRRect(
                  child: SmartImage(
                    imageUrls[2],
                    fit: BoxFit.cover,
                    isPost: true,
                  ),
                ),
                2,
                context,
              ),
            ),
            const SizedBox(height: 5),
            Expanded(
              flex: 1,
              child: Stack(
                alignment: Alignment.center,
                fit: StackFit.expand,
                children: [
                  _wrapWithHeroAndGesture(
                    ClipRRect(
                      borderRadius: const BorderRadius.only(
                        bottomRight: Radius.circular(20),
                      ),
                      child: SmartImage(
                        imageUrls[3],
                        fit: BoxFit.cover,
                        isPost: true,
                      ),
                    ),
                    3,
                    context,
                  ),
                  Positioned.fill(
                    child: GestureDetector(
                      onTap: () => _onImageTap(context, 3),
                      child: Container(
                        alignment: Alignment.center,
                        decoration: BoxDecoration(
                          color: Colors.black.withOpacity(0.5),
                          borderRadius: const BorderRadius.only(
                            bottomRight: Radius.circular(20),
                          ),
                        ),
                        child: Text(
                          '+${imageUrls.length - 4}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 22,
                            fontFamily: "Nulito",
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ];
  }
}
