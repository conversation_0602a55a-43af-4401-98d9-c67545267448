import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/widget/images/avatar_widget.dart';

FlexibleSpaceBar spaceBar(bool scrolled) {
  return FlexibleSpaceBar(
    expandedTitleScale: 1,
    collapseMode: CollapseMode.none,
    title: BlocBuilder<ProfileCubit, ProfileState>(
      builder: (context, state) {
        return Container(
          height: 52,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              SizedBox(
                width: 116,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    AvatarWidget(
                      size: 32,
                      imageUrl: state.userModel?.avatarUrl,
                      name: state.userModel?.username ?? '',
                    ),
                  ],
                ),
              ),
              SvgPicture.asset(
                Assets.icons.icLogoHome.path,
                width: 36,
                height: 36,
              ),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SvgPicture.asset(
                    Assets.icons.icSearch.path,
                    width: 36,
                    height: 36,
                  ),
                  const SizedBox(width: 8),
                  SvgPicture.asset(
                    Assets.icons.icNotification.path,
                    width: 36,
                    height: 36,
                  ),

                  const SizedBox(width: 8),
                  GestureDetector(
                    onTap: () {
                      context.push(RouterEnums.chatAiGao.routeName);
                    },
                    child: Assets.icons.icHomeAi.image(width: 36, height: 36),
                    // child: SvgPicture.asset(
                    //   Assets.icons.icHomeAi.path,
                    //   width: 28,
                    //   height: 28,
                    // ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    ),
    centerTitle: false,
  );
}
