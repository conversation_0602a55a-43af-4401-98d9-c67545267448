import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/core/constant/enum_constant.dart';
import 'package:toii_social/cubit/auth/register/register_create_wallet/register_create_wallet_cubit.dart';
import 'package:toii_social/cubit/auth/register/register_verify_step/register_verify_step_cubit.dart';
import 'package:toii_social/cubit/auth/register/username_validation/username_validation_cubit.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/generated/l10n.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/utils/widget/dismiss_keyboard_widget.dart';
import 'package:toii_social/widget/button/button.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';
import 'package:toii_social/widget/text_field.dart/text_field.dart';

class RegisterCreateNameScreen extends StatefulWidget {
  final RegisterType registerType;
  const RegisterCreateNameScreen({super.key, required this.registerType});

  @override
  State<RegisterCreateNameScreen> createState() =>
      _RegisterCreateNameScreenState();
}

class _RegisterCreateNameScreenState extends State<RegisterCreateNameScreen> {
  final _userNameController = TextEditingController();
  bool _isEnabledButton = false;

  var _errorUserName = '';
  late UsernameValidationCubit _usernameValidationCubit;

  // Add a timer for debouncing
  Timer? _debounce;

  @override
  void initState() {
    super.initState();
    _usernameValidationCubit = UsernameValidationCubit();
  }

  @override
  void dispose() {
    _usernameValidationCubit.close();
    _userNameController.dispose();
    // Cancel the timer if it's still running
    if (_debounce?.isActive ?? false) _debounce!.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DismissKeyboardWidget(child: _bodyWidget());
  }

  Widget _bottomWidget() {
    return BlocConsumer<RegisterCreateWalletCubit, RegisterCreateWalletState>(
      listener: (context, state) async {
        if (state.status.isSuccess) {
          context.go(RouterEnums.inital.routeName);
        } else if (state.status.isFailure) {
          context.showSnackbar(message: state.errorMessage!);
        }
      },
      builder: (context, state) {
        return Container(
          margin: EdgeInsets.only(
            // left: 16,
            // right: 16,
            bottom: MediaQuery.of(context).padding.bottom + 8,
          ),
          child: TSButton.primary(
            title: S.current.register_continue,
            isEnabled: _isEnabledButton,
            isLoading: state.status.isLoading,
            onPressed: () {
              context.read<RegisterVerifyStepCubit>().updateRequestInfo(
                userName: _userNameController.text,
              );
              if (widget.registerType == RegisterType.wallet) {
                context
                    .read<RegisterCreateWalletCubit>()
                    .completeRegistrationWallet(
                      userName: _userNameController.text,
                    );
              } else if (widget.registerType == RegisterType.social) {
                context.read<RegisterVerifyStepCubit>().updateRequestInfo(
                  registerType: "social",
                );
                context.read<RegisterVerifyStepCubit>().isSocial = true;
                context.push(RouterEnums.registerCreatePin.routeName);
              } else {
                context.push(RouterEnums.registerCreatePassword.routeName);
              }
            },
          ),
        );
      },
    );
  }

  Widget _userNameFormWidget() {
    return BlocBuilder<UsernameValidationCubit, UsernameValidationState>(
      bloc: _usernameValidationCubit,
      builder: (context, validationState) {
        return TTextField(
          autofocus: true,
          labelText: S.current.register_username_field_label,
          hintText: S.current.register_username_field_label,
          onChanged: (value) {
            // Cancel the previous timer if it's still running
            if (_debounce?.isActive ?? false) _debounce!.cancel();

            // Start a new timer
            _debounce = Timer(const Duration(milliseconds: 500), () {
              // Trigger username validation
              _usernameValidationCubit.validateUsername(value);

              // Update button state based on validation
              setState(() {
                if (value.trim().isEmpty) {
                  _isEnabledButton = false;
                  _errorUserName = S.current.register_create_username_error;
                } else if (validationState.status.isLoading) {
                  _isEnabledButton = false;
                  _errorUserName = '';
                } else if (validationState.status.isSuccess) {
                  _isEnabledButton = validationState.isAvailable;
                  _errorUserName =
                      validationState.isAvailable
                          ? ''
                          : (validationState.errorMessage ??
                              'Username is not available');
                } else if (validationState.status.isFailure) {
                  _isEnabledButton = false;
                  _errorUserName =
                      validationState.errorMessage ??
                      'Failed to check username availability';
                } else {
                  _isEnabledButton = false;
                  _errorUserName = '';
                }
              });
            });
          },
          errorText: _errorUserName.isNotEmpty ? _errorUserName : null,
          textController: _userNameController,
          suffixIcon:
              validationState.status.isLoading
                  ? SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                  : validationState.status.isSuccess &&
                      validationState.isAvailable
                  ? Icon(Icons.check_circle, color: themeData.primaryGreen500)
                  : null,
        );
      },
    );
  }

  Widget _bodyWidget() {
    final info = context.read<RegisterVerifyStepCubit>().requestInfo;
    return BlocProvider(
      create: (_) => RegisterCreateWalletCubit(requestInfo: info),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(height: 16),
                    Text(
                      S.current.register_create_username_title,
                      style: headlineSmall.copyColor(themeData.neutral800),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      S.current.register_create_username_subtitle,
                      style: bodyMedium.copyColor(themeData.neutral400),
                    ),

                    const SizedBox(height: 24),
                    _userNameFormWidget(),

                    Expanded(child: Container()),
                  ],
                ),
              ),
              _bottomWidget(),
            ],
          ),
        ),
      ),
    );
  }
}
