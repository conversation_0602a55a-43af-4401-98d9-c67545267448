import 'package:country_code_picker/country_code_picker.dart';
import 'package:easy_rich_text/easy_rich_text.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/core/constant/constant.dart';
import 'package:toii_social/core/constant/enum_constant.dart';
import 'package:toii_social/cubit/auth/register/choose_country/register_choose_country_cubit.dart';
import 'package:toii_social/cubit/auth/register/register_validate/register_validate_cubit.dart';
import 'package:toii_social/cubit/auth/register/request_otp/register_request_otp_cubit.dart';
import 'package:toii_social/cubit/theme/theme_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/generated/l10n.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/register/register/widget/bottom_sheet_country.dart';
import 'package:toii_social/screen/register/verify/register_verify_phone_screen.dart';
import 'package:toii_social/utils/url_luncher/url_luncher.dart';
import 'package:toii_social/utils/utils/validator.dart';
import 'package:toii_social/widget/button/button.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';
import 'package:toii_social/widget/scaffords/scafford.dart';
import 'package:toii_social/widget/snack_bar/snackbar.dart';
import 'package:toii_social/widget/text_field.dart/text_field.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  RegisterType _registerType = RegisterType.phone;
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  final FocusNode _focusEmail = FocusNode();
  final FocusNode _focusPhone = FocusNode();
  final _registerValidateCubit = RegisterValidateCubit();
  final _registerChooseCountryCubit = RegisterChooseCountryCubit();
  @override
  void initState() {
    super.initState();
    _focusEmail.addListener(_onFocusChangeEmail);
    _focusPhone.addListener(_onFocusChangePhone);
  }

  @override
  void dispose() {
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
    _focusEmail.removeListener(_onFocusChangeEmail);
    _focusEmail.dispose();
    _focusPhone.removeListener(_onFocusChangePhone);
    _focusPhone.dispose();
  }

  void _onFocusChangeEmail() {
    if (_focusEmail.hasFocus) {
      _registerValidateCubit.clearErrorEmail();
    } else {
      _registerValidateCubit.validateForm(email: _emailController.text);
    }
  }

  void _onFocusChangePhone() {
    if (_focusPhone.hasFocus) {
      _registerValidateCubit.clearErrorPhone();
    } else {
      _registerValidateCubit.validateForm(phoneNumber: _phoneController.text);
    }
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (_) => _registerChooseCountryCubit),
        BlocProvider(create: (_) => RegisterRequestOtpCubit()),
        BlocProvider(create: (_) => _registerValidateCubit),
      ],
      child: BaseScaffold(
        title: Text(''),
        body: GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            SystemChannels.textInput.invokeMethod('TextInput.hide');
          },
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 12),
                  Text(
                    S.current.register_enter_title,
                    style: headlineSmall.copyColor(themeData.neutral800),
                  ),
                  const SizedBox(height: 16),
                  _typeRegisterWidget(),
                  const SizedBox(height: 32),
                  _registerType == RegisterType.phone
                      ? _phoneFormWidget()
                      : _emailFormWidget(),
                  Expanded(child: Container()),
                  _agreementWidget(),

                  const SizedBox(height: 16),
                  BlocConsumer<
                    RegisterRequestOtpCubit,
                    RegisterRequestOtpState
                  >(
                    listener: (context, state) {
                      if (state.status.isSuccess) {
                        final String value;
                        if (_registerType == RegisterType.phone) {
                          value =
                              _registerChooseCountryCubit.country.dialCode! +
                              _phoneController.text.formattedPhoneNumber;
                        } else {
                          value = _emailController.text;
                        }
                        final arg = RegisterVerifyPhoneArguments(
                          registerType: _registerType,
                          value: value,
                        );
                        context.push(
                          RouterEnums.registerVerifyPhone.routeName,
                          extra: arg,
                        );
                      } else if (state.status.isFailure) {
                        context.showSnackbar(message: state.errorMessage ?? "");
                      }
                    },
                    builder: (context, state) {
                      return BlocBuilder<
                        RegisterValidateCubit,
                        RegisterValidateState
                      >(
                        builder: (BuildContext ctx, stateValidate) {
                          return TSButton.primary(
                            isHideKeyBoard: true,
                            title: S.current.register_continue,
                            isEnabled: stateValidate.isEnableButton,
                            onPressed: () {
                              if (_registerType == RegisterType.phone) {
                                context
                                    .read<RegisterRequestOtpCubit>()
                                    .requestOtpPhone(
                                      code:
                                          _registerChooseCountryCubit
                                              .country
                                              .dialCode!,
                                      phoneNumber: _phoneController.text,
                                    );
                              } else {
                                context
                                    .read<RegisterRequestOtpCubit>()
                                    .requestOtpEmail(
                                      email: _emailController.text,
                                    );
                              }
                            },
                            isLoading: state.status.isLoading,
                          );
                        },
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _typeRegisterWidget() {
    Widget tab({
      required String title,
      bool isSelected = false,
      required VoidCallback onTap,
    }) {
      return InkWell(
        borderRadius: BorderRadius.circular(32),
        onTap: onTap,
        child: Container(
          decoration: BoxDecoration(
            color:
                isSelected ? themeData.primaryGreen500 : themeData.neutral100,
            borderRadius: BorderRadius.circular(32),
            border: isSelected ? null : Border.all(color: themeData.neutral200),
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
          ).copyWith(bottom: 2),
          height: 42,
          child: Center(
            child: Text(
              title,
              style: labelLarge.copyColor(
                isSelected ? themeData.white900 : themeData.neutral400,
              ),
            ),
          ),
        ),
      );
    }

    return Row(
      children: [
        tab(
          title: S.current.register_option_phone,
          isSelected: _registerType == RegisterType.phone,
          onTap: () {
            setState(() {
              _registerType = RegisterType.phone;
            });
          },
        ),
        const SizedBox(width: 8),
        tab(
          title: S.current.register_option_email,
          isSelected: _registerType == RegisterType.email,
          onTap: () {
            setState(() {
              _registerType = RegisterType.email;
            });
          },
        ),
      ],
    );
  }

  Widget _emailFormWidget() {
    return BlocBuilder<RegisterValidateCubit, RegisterValidateState>(
      buildWhen:
          (previous, current) => previous.errorEmail != current.errorEmail,
      builder: (BuildContext ctx, state) {
        return TTextField(
          labelText: S.current.register_option_email,
          hintText: S.current.register_option_email,
          onChanged: (value) {
            _registerValidateCubit.checkEnableButton(
              _registerType == RegisterType.phone,
              email: _emailController.text,
              phoneNumber: _phoneController.text,
            );
          },
          focusNode: _focusEmail,
          onEditingComplete: () {
            context.read<RegisterValidateCubit>().validateForm(
              email: _emailController.text,
            );
            _focusEmail.unfocus();
          },
          errorText:
              state.errorEmail?.isNotEmpty == true ? state.errorEmail : null,
          textController: _emailController,
        );
      },
    );
  }

  Widget _phoneFormWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          S.current.register_label_phone_number,
          style: labelMedium.copyColor(themeData.primaryGreen500),
        ),
        const SizedBox(height: 8),

        Row(
          children: [
            BlocBuilder<RegisterChooseCountryCubit, RegisterChooseCountryState>(
              builder: (context, state) {
                final country =
                    context.read<RegisterChooseCountryCubit>().country;
                return GestureDetector(
                  onTap: () async {
                    final result = await showBottomSheetCountry(
                      context: context,
                    );
                    if (result is CountryCode) {
                      if (context.mounted) {
                        context
                            .read<RegisterChooseCountryCubit>()
                            .setCountryCode(result.code!);
                      }
                    }
                  },
                  child: Container(
                    height: 56,
                    width: 104,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: themeData.neutral200),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(
                          width: 24,
                          height: 24,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(100),
                            child: Image.asset(
                              country.flagUri!,
                              fit: BoxFit.cover,
                              package: 'country_code_picker',
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          country.dialCode!,
                          style: bodyLarge.copyColor(themeData.neutral400),
                        ),
                        const SizedBox(width: 4),
                        SvgPicture.asset(
                          Assets.icons.icArrowDown.path,
                          colorFilter: ColorFilter.mode(
                            themeData.neutral500,
                            BlendMode.srcIn,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
            const SizedBox(width: 6),
            Expanded(
              child: BlocBuilder<RegisterValidateCubit, RegisterValidateState>(
                buildWhen:
                    (previous, current) =>
                        previous.errorPhone != current.errorPhone,
                builder: (BuildContext ctx, state) {
                  return TTextField(
                    onChanged: (value) {
                      _registerValidateCubit.checkEnableButton(
                        _registerType == RegisterType.phone,
                        email: _emailController.text,
                        phoneNumber: _phoneController.text,
                      );
                    },
                    labelText: S.current.register_option_phone,
                    hintText: S.current.register_option_phone,
                    focusNode: _focusPhone,
                    onEditingComplete: () {
                      context.read<RegisterValidateCubit>().validateForm(
                        phoneNumber: _phoneController.text,
                      );
                      _focusPhone.unfocus();
                    },
                    errorText:
                        state.errorPhone?.isNotEmpty == true
                            ? state.errorPhone
                            : null,
                    textController: _phoneController,
                  );
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _agreementWidget() {
    return BlocBuilder<RegisterValidateCubit, RegisterValidateState>(
      buildWhen: (previous, current) => previous.isAgreed != current.isAgreed,
      builder: (BuildContext ctx, state) {
        return GestureDetector(
          onTap: () {
            _registerValidateCubit.setAgreed();
            _registerValidateCubit.checkEnableButton(
              _registerType == RegisterType.phone,
              email: _emailController.text,
              phoneNumber: _phoneController.text,
            );
          },
          child: Row(
            children: [
              SvgPicture.asset(
                state.isAgreed
                    ? Assets.icons.icChecked.path
                    : Assets.icons.icCheckbox.path,
              ),

              const SizedBox(width: 8),
              Expanded(
                child: EasyRichText(
                  S.current.register_terms_agreement,
                  defaultStyle: bodyMedium.copyColor(themeData.neutral400),
                  patternList: [
                    EasyRichTextPattern(
                      targetString: S.current.register_terms_of_service,
                      style: bodyMedium.copyColor(themeData.primaryGreen500),
                      recognizer:
                          TapGestureRecognizer()
                            ..onTap = () {
                              launchUrlCustom(Constant.baseUrlPrivacy);
                            },
                    ),
                    EasyRichTextPattern(
                      targetString: S.current.register_privacy_policy,
                      style: bodyMedium.copyColor(themeData.primaryGreen500),
                      recognizer:
                          TapGestureRecognizer()
                            ..onTap = () {
                              launchUrlCustom(Constant.baseUrlPrivacy);
                            },
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
