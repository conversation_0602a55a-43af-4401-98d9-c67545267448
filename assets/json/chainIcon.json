[{"chainId": 1, "name": "Ethereum", "icon": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/ethereum/info/logo.png", "backup": "https://static.particle.network/token-list/ethereum/native.png"}, {"chainId": 56, "name": "BNB Smart Chain", "icon": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/smartchain/info/logo.png", "backup": "https://static.particle.network/token-list/bsc/native.png"}, {"chainId": 137, "name": "Polygon", "icon": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/polygon/info/logo.png", "backup": "https://static.particle.network/token-list/polygon/native.png"}, {"chainId": 10, "name": "Optimism", "icon": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/optimism/info/logo.png", "backup": "https://static.particle.network/token-list/optimism/native.png"}, {"chainId": 42161, "name": "Arbitrum One", "icon": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/arbitrum/info/logo.png", "backup": "https://static.particle.network/token-list/arbitrum/native.png"}, {"chainId": 43114, "name": "Avalanche", "icon": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/avalanche/info/logo.png", "backup": "https://static.particle.network/token-list/avalanche/native.png"}, {"chainId": 8453, "name": "Base", "icon": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/base/info/logo.png", "backup": "https://static.particle.network/token-list/base/native.png"}, {"chainId": 250, "name": "<PERSON><PERSON>", "icon": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/fantom/info/logo.png", "backup": "https://static.particle.network/token-list/fantom/native.png"}, {"chainId": 324, "name": "zkSync Era", "icon": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/zksync/info/logo.png", "backup": "https://static.particle.network/token-list/zksync/native.png"}, {"chainId": 1101, "name": "Polygon zkEVM", "icon": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/polygonzkevm/info/logo.png", "backup": "https://static.particle.network/token-list/polygonzkevm/native.png"}, {"chainId": 59144, "name": "Linea", "icon": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/linea/info/logo.png", "backup": "https://static.particle.network/token-list/linea/native.png"}, {"chainId": 5000, "name": "Mantle", "icon": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/mantle/info/logo.png", "backup": "https://static.particle.network/token-list/mantle/native.png"}, {"chainId": 204, "name": "opBNB", "icon": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/opbnb/info/logo.png", "backup": "https://static.particle.network/token-list/opbnb/native.png"}, {"chainId": 100, "name": "Gnosis", "icon": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/gnosis/info/logo.png", "backup": "https://static.particle.network/token-list/gnosis/native.png"}, {"chainId": 1088, "name": "<PERSON><PERSON>", "icon": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/metis/info/logo.png", "backup": "https://static.particle.network/token-list/metis/native.png"}, {"chainId": 1284, "name": "Moonbeam", "icon": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/moonbeam/info/logo.png", "backup": "https://static.particle.network/token-list/moonbeam/native.png"}, {"chainId": 42220, "name": "<PERSON><PERSON>", "icon": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/celo/info/logo.png", "backup": "https://static.particle.network/token-list/celo/native.png"}, {"chainId": 9001, "name": "Evmos", "icon": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/evmos/info/logo.png", "backup": "https://static.particle.network/token-list/evmos/native.png"}, {"chainId": 888, "name": "Wanchain", "icon": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/wanchain/info/logo.png", "backup": "https://static.particle.network/token-list/wanchain/native.png"}, {"chainId": 81457, "name": "Blast", "icon": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/blast/info/logo.png", "backup": "https://static.particle.network/token-list/blast/native.png"}]