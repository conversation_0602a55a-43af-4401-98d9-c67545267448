[{"name": "TOII Testnet", "title": "TOII Testnet", "chain": "TOII", "icon": "toii", "rpc": ["https://toii-testnet.rpc.caldera.xyz/http"], "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "TOII", "decimals": 18}, "chainId": 472025}, {"name": "Ethereum Mainnet", "chain": "ETH", "icon": "ethereum", "rpc": ["https://mainnet.infura.io/v3/${INFURA_API_KEY}", "wss://mainnet.infura.io/ws/v3/${INFURA_API_KEY}", "https://api.mycryptoapi.com/eth", "https://cloudflare-eth.com", "https://ethereum-rpc.publicnode.com", "wss://ethereum-rpc.publicnode.com", "https://mainnet.gateway.tenderly.co", "wss://mainnet.gateway.tenderly.co", "https://rpc.blocknative.com/boost", "https://rpc.flashbots.net", "https://rpc.flashbots.net/fast", "https://rpc.mevblocker.io", "https://rpc.mevblocker.io/fast", "https://rpc.mevblocker.io/noreverts", "https://rpc.mevblocker.io/fullprivacy", "https://eth.drpc.org", "wss://eth.drpc.org", "https://api.securerpc.com/v1"], "features": [{"name": "EIP155"}, {"name": "EIP1559"}], "faucets": [], "nativeCurrency": {"name": "<PERSON><PERSON>", "symbol": "ETH", "decimals": 18}, "infoURL": "https://ethereum.org", "shortName": "eth", "chainId": 1, "networkId": 1, "slip44": 60, "ens": {"registry": "******************************************"}, "explorers": [{"name": "etherscan", "url": "https://etherscan.io", "standard": "EIP3091"}, {"name": "blockscout", "url": "https://eth.blockscout.com", "icon": "blockscout", "standard": "EIP3091"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://ethereum.dex.guru", "icon": "<PERSON><PERSON><PERSON><PERSON>", "standard": "EIP3091"}, {"name": "Routescan", "url": "https://ethereum.routescan.io", "standard": "EIP3091"}]}, {"name": "BNB Smart Chain Mainnet", "chain": "BSC", "rpc": ["https://bsc-dataseed1.bnbchain.org", "https://bsc-dataseed2.bnbchain.org", "https://bsc-dataseed3.bnbchain.org", "https://bsc-dataseed4.bnbchain.org", "https://bsc-dataseed1.defibit.io", "https://bsc-dataseed2.defibit.io", "https://bsc-dataseed3.defibit.io", "https://bsc-dataseed4.defibit.io", "https://bsc-dataseed1.ninicoin.io", "https://bsc-dataseed2.ninicoin.io", "https://bsc-dataseed3.ninicoin.io", "https://bsc-dataseed4.ninicoin.io", "https://bsc-rpc.publicnode.com", "wss://bsc-rpc.publicnode.com", "wss://bsc-ws-node.nariox.org"], "faucets": [], "nativeCurrency": {"name": "BNB Chain Native Token", "symbol": "BNB", "decimals": 18}, "infoURL": "https://www.bnbchain.org/en", "shortName": "bnb", "chainId": 56, "networkId": 56, "slip44": 714, "explorers": [{"name": "bscscan", "url": "https://bscscan.com", "standard": "EIP3091"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://bnb.dex.guru", "icon": "<PERSON><PERSON><PERSON><PERSON>", "standard": "EIP3091"}]}]