[{"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-77c9e09", "name": "<PERSON><PERSON>", "color": 4288585374, "type": 0, "rpcUrls": ["https://toii-testnet.rpc.caldera.xyz/http"], "decimals": 18, "chainId": 472025, "network": null, "contractAddress": null, "valueUsd": null, "symbol": "TOII", "cgSymbol": "ethereum", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-77c9231ce09", "name": "Ethereum", "color": 4288585374, "type": 0, "icon": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/ethereum/assets/******************************************/logo.png", "rpcUrls": ["https://api.mycryptoapi.com/eth", "https://cloudflare-eth.com", "https://ethereum-rpc.publicnode.com", "https://mainnet.gateway.tenderly.co", "https://rpc.blocknative.com/boost", "https://rpc.flashbots.net", "https://rpc.flashbots.net/fast", "https://rpc.mevblocker.io", "https://rpc.mevblocker.io/fast", "https://rpc.mevblocker.io/noreverts", "https://rpc.mevblocker.io/fullprivacy", "https://eth.drpc.org", "https://api.securerpc.com/v1"], "decimals": 18, "chainId": 1, "network": null, "contractAddress": null, "explorers": ["https://etherscan.io/", "https://ethereum.dex.guru/", "https://ethereum.routescan.io/"], "valueUsd": null, "symbol": "ETH", "cgSymbol": "ethereum", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "a6e6b336-616b-4502-af5f-753c7731ce10", "name": "BSC", "color": 4293966091, "type": 0, "icon": "https://raw.githubusercontent.com/lifinance/types/main/src/assets/icons/chains/bsc.svg", "rpcUrls": ["https://bsc-dataseed1.bnbchain.org", "https://bsc-dataseed2.bnbchain.org", "https://bsc-dataseed3.bnbchain.org", "https://bsc-dataseed4.bnbchain.org", "https://bsc-dataseed1.defibit.io", "https://bsc-dataseed2.defibit.io", "https://bsc-dataseed3.defibit.io", "https://bsc-dataseed4.defibit.io", "https://bsc-dataseed1.ninicoin.io", "https://bsc-dataseed2.ninicoin.io", "https://bsc-dataseed3.ninicoin.io", "https://bsc-dataseed4.ninicoin.io", "https://bsc-rpc.publicnode.com"], "decimals": 18, "chainId": 56, "network": null, "contractAddress": null, "explorers": ["https://bscscan.com/", "https://bnb.dex.guru/"], "valueUsd": 0.0, "symbol": "BNB", "cgSymbol": "binancecoin", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-77c9231ce09", "name": "Ethereum", "color": 4288585374, "type": 0, "icon": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/ethereum/assets/******************************************/logo.png", "rpcUrls": ["https://api.mycryptoapi.com/eth", "https://cloudflare-eth.com", "https://ethereum-rpc.publicnode.com", "https://mainnet.gateway.tenderly.co", "https://rpc.blocknative.com/boost", "https://rpc.flashbots.net", "https://rpc.flashbots.net/fast", "https://rpc.mevblocker.io", "https://rpc.mevblocker.io/fast", "https://rpc.mevblocker.io/noreverts", "https://rpc.mevblocker.io/fullprivacy", "https://eth.drpc.org", "https://api.securerpc.com/v1"], "decimals": 18, "chainId": 1, "network": null, "contractAddress": null, "explorers": ["https://etherscan.io/", "https://ethereum.dex.guru/", "https://ethereum.routescan.io/"], "valueUsd": null, "symbol": "ETH", "cgSymbol": "ethereum", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "a6e6b336-616b-4502-af5f-753c7731ce10", "name": "BSC", "color": 4293966091, "type": 0, "icon": "https://raw.githubusercontent.com/lifinance/types/main/src/assets/icons/chains/bsc.svg", "rpcUrls": ["https://bsc-dataseed1.bnbchain.org", "https://bsc-dataseed2.bnbchain.org", "https://bsc-dataseed3.bnbchain.org", "https://bsc-dataseed4.bnbchain.org", "https://bsc-dataseed1.defibit.io", "https://bsc-dataseed2.defibit.io", "https://bsc-dataseed3.defibit.io", "https://bsc-dataseed4.defibit.io", "https://bsc-dataseed1.ninicoin.io", "https://bsc-dataseed2.ninicoin.io", "https://bsc-dataseed3.ninicoin.io", "https://bsc-dataseed4.ninicoin.io", "https://bsc-rpc.publicnode.com"], "decimals": 18, "chainId": 56, "network": null, "contractAddress": null, "explorers": ["https://bscscan.com/", "https://bnb.dex.guru/"], "valueUsd": 0.0, "symbol": "BNB", "cgSymbol": "binancecoin", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "a6e363636-616b-4502-af5f-753c77383e78", "name": "OpBNB", "color": 4293966091, "type": 0, "icon": "https://raw.githubusercontent.com/lifinance/types/main/src/assets/icons/chains/bsc.svg", "rpcUrls": ["https://opbnb-mainnet-rpc.bnbchain.org", "https://opbnb-mainnet.nodereal.io/v1/64a9df0874fb4a93b9d0a3849de012d3", "https://opbnb-mainnet.nodereal.io/v1/e9a36765eb8a40b9bd12e680a1fd2bc5", "https://opbnb-rpc.publicnode.com", "https://opbnb.drpc.org"], "decimals": 18, "chainId": 204, "network": null, "contractAddress": null, "explorers": ["https://opbnb.bscscan.com/", "https://mainnet.opbnbscan.com/"], "valueUsd": null, "symbol": "BNB", "cgSymbol": "binancecoin", "networkType": 0, "refToken": "******************************************", "refTokenChainId": 56}, {"canDisplay": false, "cryptoId": "42e6b336-616b-7702-af5f-453c9231ce11", "name": "Arbitrum", "color": 4279413503, "type": 0, "icon": "https://raw.githubusercontent.com/lifinance/types/main/src/assets/icons/chains/arbitrum.svg", "rpcUrls": ["https://arb1.arbitrum.io/rpc", "https://arbitrum-one-rpc.publicnode.com"], "decimals": 18, "chainId": 42161, "network": null, "contractAddress": null, "explorers": ["https://arbiscan.io/"], "valueUsd": null, "symbol": "ETH", "cgSymbol": "ethereum", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-488c9277ce12", "name": "Base", "color": 4278211327, "type": 0, "icon": "https://raw.githubusercontent.com/lifinance/types/main/src/assets/icons/chains/base.svg", "rpcUrls": ["https://mainnet.base.org/", "https://developer-access-mainnet.base.org/", "https://base.gateway.tenderly.co", "https://base-rpc.publicnode.com"], "decimals": 18, "chainId": 8453, "network": null, "contractAddress": null, "explorers": ["https://basescan.org/", "https://base.blockscout.com/"], "valueUsd": null, "symbol": "ETH", "cgSymbol": "ethereum", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-453c99931ce13", "name": "Blast", "color": 4294769667, "type": 0, "icon": "https://raw.githubusercontent.com/lifinance/types/main/src/assets/icons/chains/blast.svg", "rpcUrls": ["https://rpc.blast.io", "https://rpc.ankr.com/blast", "https://blast.din.dev/rpc", "https://blastl2-mainnet.public.blastapi.io", "https://blast.blockpi.network/v1/rpc/public", "https://blast-rpc.publicnode.com"], "decimals": 18, "chainId": 81457, "network": null, "contractAddress": null, "explorers": ["https://blastscan.io/", "https://blastexplorer.io/"], "valueUsd": null, "symbol": "ETH", "cgSymbol": "ethereum", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-453c92521ce14", "name": "Avalanche", "color": 4293411138, "type": 0, "icon": "https://raw.githubusercontent.com/lifinance/types/main/src/assets/icons/chains/avalanche.svg", "rpcUrls": ["https://api.avax.network/ext/bc/C/rpc", "https://avalanche-c-chain-rpc.publicnode.com"], "decimals": 18, "chainId": 43114, "network": null, "contractAddress": null, "explorers": ["https://snowtrace.io/"], "valueUsd": null, "symbol": "AVAX", "cgSymbol": "avalanche-2", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-453c92262ce15", "name": "Polygon", "color": **********, "type": 0, "icon": "https://static.debank.com/image/matic_token/logo_url/matic/6f5a6b6f0732a7a235131bd7804d357c.png", "rpcUrls": ["https://polygon-rpc.com/", "https://rpc-mainnet.matic.network", "https://matic-mainnet.chainstacklabs.com", "https://rpc-mainnet.maticvigil.com", "https://rpc-mainnet.matic.quiknode.pro", "https://matic-mainnet-full-rpc.bwarelabs.com", "https://polygon-bor-rpc.publicnode.com", "https://polygon.gateway.tenderly.co", "https://polygon.drpc.org"], "decimals": 18, "chainId": 137, "network": null, "contractAddress": null, "explorers": ["https://polygonscan.com/", "https://polygon.dex.guru/"], "valueUsd": null, "symbol": "POL", "cgSymbol": "matic-network", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-617b-4502-af5f-453c923626e16", "name": "<PERSON><PERSON>", "color": **********, "type": 0, "icon": "https://raw.githubusercontent.com/lifinance/types/main/src/assets/icons/chains/scroll.svg", "rpcUrls": ["https://rpc.scroll.io", "https://rpc.ankr.com/scroll", "https://scroll-mainnet.chainstacklabs.com", "https://scroll-rpc.publicnode.com"], "decimals": 18, "chainId": 534352, "network": null, "contractAddress": null, "explorers": ["https://scrollscan.com/"], "valueUsd": null, "symbol": "ETH", "cgSymbol": "ethereum", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b335-616b-4502-af5f-453c9231717", "name": "Optimism", "color": 4294902788, "type": 0, "icon": "https://raw.githubusercontent.com/lifinance/types/main/src/assets/icons/chains/optimism.svg", "rpcUrls": ["https://mainnet.optimism.io", "https://optimism-rpc.publicnode.com", "https://optimism.gateway.tenderly.co", "https://optimism.drpc.org"], "decimals": 18, "chainId": 10, "network": null, "contractAddress": null, "explorers": ["https://optimistic.etherscan.io/", "https://optimism.blockscout.com/", "https://optimism.dex.guru/", "https://mainnet.superscan.network/"], "valueUsd": null, "symbol": "ETH", "cgSymbol": "ethereum", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-453c92424ce18", "name": "Linea", "color": 4279374354, "type": 0, "icon": "https://raw.githubusercontent.com/lifinance/types/main/src/assets/icons/chains/linea.svg", "rpcUrls": ["https://rpc.linea.build", "https://linea-rpc.publicnode.com"], "decimals": 18, "chainId": 59144, "network": null, "contractAddress": null, "explorers": ["https://lineascan.build/"], "valueUsd": null, "symbol": "ETH", "cgSymbol": "ethereum", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-473c9252ce19", "name": "zkSync", "color": 4287269514, "type": 0, "icon": "https://raw.githubusercontent.com/lifinance/types/main/src/assets/icons/chains/zksync.svg", "rpcUrls": ["https://mainnet.era.zksync.io", "https://zksync.drpc.org"], "decimals": 18, "chainId": 324, "network": null, "contractAddress": null, "explorers": ["https://explorer.zksync.io/"], "valueUsd": null, "symbol": "ETH", "cgSymbol": "ethereum", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-453c252ce20", "name": "Polygon zkEVM", "color": 4286267364, "type": 0, "icon": "https://raw.githubusercontent.com/lifinance/types/main/src/assets/icons/chains/polygon.svg", "rpcUrls": ["https://zkevm-rpc.com", "https://polygon-zkevm.drpc.org"], "decimals": 18, "chainId": 1101, "network": null, "contractAddress": null, "explorers": ["https://zkevm.polygonscan.com/"], "valueUsd": null, "symbol": "ETH", "cgSymbol": "ethereum", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-2553c9231242", "name": "Gnosis", "color": 4278483291, "type": 0, "icon": "https://raw.githubusercontent.com/trustwallet/assets/master/blockchains/ethereum/assets/******************************************/logo.png", "rpcUrls": ["https://rpc.gnosischain.com", "https://rpc.gnosis.gateway.fm", "https://rpc.ankr.com/gnosis", "https://gnosischain-rpc.gateway.pokt.network", "https://gnosis-mainnet.public.blastapi.io", "https://gnosis.api.onfinality.io/public", "https://gnosis.blockpi.network/v1/rpc/public", "https://web3endpoints.com/gnosischain-mainnet", "https://gnosis.oat.farm", "https://gnosis-rpc.publicnode.com"], "decimals": 18, "chainId": 100, "network": null, "contractAddress": null, "explorers": ["https://gnosisscan.io/", "https://gnosis.blockscout.com/", "https://gnosis.dex.guru/"], "valueUsd": null, "symbol": "xDAI", "cgSymbol": "xdai", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-43636c9231ce22", "name": "<PERSON><PERSON>", "color": **********, "type": 0, "icon": "https://static.debank.com/image/ftm_token/logo_url/ftm/33fdb9c5067e94f3a1b9e78f6fa86984.png", "rpcUrls": ["https://rpc.ftm.tools", "https://fantom-rpc.publicnode.com", "https://fantom.drpc.org"], "decimals": 18, "chainId": 250, "network": null, "contractAddress": null, "explorers": ["https://ftmscan.com/", "https://fantom.dex.guru/"], "valueUsd": null, "symbol": "FTM", "cgSymbol": "fantom", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-453c923535e23", "name": "Moonriver", "color": **********, "type": 0, "icon": "https://raw.githubusercontent.com/lifinance/types/main/src/assets/icons/chains/moonriver.svg", "rpcUrls": ["https://rpc.api.moonriver.moonbeam.network", "https://moonriver.public.blastapi.io", "https://moonriver-rpc.dwellir.com", "https://moonriver.api.onfinality.io/public", "https://moonriver.unitedbloc.com", "https://moonriver-rpc.publicnode.com", "https://moonriver.drpc.org"], "decimals": 18, "chainId": 1285, "network": null, "contractAddress": null, "explorers": ["https://moonriver.moonscan.io/"], "valueUsd": null, "symbol": "MOVR", "cgSymbol": "moonriver", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-453c923535e24", "name": "Moonbeam", "color": 4280357944, "type": 0, "icon": "https://s2.coinmarketcap.com/static/img/coins/64x64/6836.png", "rpcUrls": ["https://rpc.api.moonbeam.network", "https://moonbeam.public.blastapi.io", "https://moonbeam-rpc.dwellir.com", "https://moonbeam.api.onfinality.io/public", "https://moonbeam.unitedbloc.com", "https://moonbeam-rpc.publicnode.com", "https://moonbeam.drpc.org"], "decimals": 18, "chainId": 1284, "network": null, "contractAddress": null, "explorers": ["https://moonbeam.moonscan.io/"], "valueUsd": null, "symbol": "GLMR", "cgSymbol": "moonbeam", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-453c923134345", "name": "FUSE", "color": 4290050490, "type": 0, "icon": "https://s2.coinmarketcap.com/static/img/coins/64x64/5634.png", "rpcUrls": ["https://rpc.fuse.io", "https://fuse.drpc.org"], "decimals": 18, "chainId": 122, "network": null, "contractAddress": null, "explorers": ["https://explorer.fuse.io/"], "valueUsd": null, "symbol": "FUSE", "cgSymbol": "fuse-network-token", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-453c923125256", "name": "<PERSON><PERSON>", "color": 4292343557, "type": 0, "icon": "https://raw.githubusercontent.com/lifinance/types/main/src/assets/icons/chains/boba.svg", "rpcUrls": ["https://mainnet.boba.network", "https://replica.boba.network", "https://boba-ethereum.gateway.tenderly.co", "https://gateway.tenderly.co/public/boba-ethereum", "https://boba-eth.drpc.org"], "decimals": 18, "chainId": 288, "network": null, "contractAddress": null, "explorers": ["https://bobascan.com/"], "valueUsd": null, "symbol": "ETH", "cgSymbol": "ethereum", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-453c923535327", "name": "Mode", "color": **********, "type": 0, "icon": "https://raw.githubusercontent.com/lifinance/types/main/src/assets/icons/chains/mode.svg", "rpcUrls": ["https://mainnet.mode.network", "https://mode.drpc.org"], "decimals": 18, "chainId": 34443, "network": null, "contractAddress": null, "explorers": ["https://explorer.mode.network/", "https://modescan.io/"], "valueUsd": null, "symbol": "ETH", "cgSymbol": "ethereum", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-45244231ce28", "name": "<PERSON><PERSON>", "color": 4278246092, "type": 0, "icon": "https://s2.coinmarketcap.com/static/img/coins/64x64/9640.png", "rpcUrls": ["https://andromeda.metis.io/?owner=1088", "https://metis.drpc.org", "https://metis-rpc.publicnode.com"], "decimals": 18, "chainId": 1088, "network": null, "contractAddress": null, "explorers": ["https://andromeda-explorer.metis.io/"], "valueUsd": null, "symbol": "METIS", "cgSymbol": "metis-token", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-453c92353529", "name": "Lisk", "color": 4279922671, "type": 0, "icon": "https://raw.githubusercontent.com/lifinance/types/main/src/assets/icons/chains/lisk.svg", "rpcUrls": ["https://rpc.api.lisk.com"], "decimals": 18, "chainId": 1135, "network": null, "contractAddress": null, "explorers": ["https://blockscout.lisk.com/"], "valueUsd": null, "symbol": "ETH", "cgSymbol": "ethereum", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-453c93737e30", "name": "<PERSON><PERSON><PERSON>", "color": **********, "type": 0, "icon": "https://raw.githubusercontent.com/lifinance/types/main/src/assets/icons/chains/unichain.svg", "rpcUrls": ["https://mainnet.unichain.org", "https://unichain-rpc.publicnode.com"], "decimals": 18, "chainId": 130, "network": null, "contractAddress": null, "explorers": ["https://uniscan.xyz/", "https://unichain.blockscout.com/"], "valueUsd": null, "symbol": "ETH", "cgSymbol": "ethereum", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-453c938383e31", "name": "Aurora", "color": **********, "type": 0, "icon": "https://static.debank.com/image/aurora_token/logo_url/aurora/d61441782d4a08a7479d54aea211679e.png", "rpcUrls": ["https://mainnet.aurora.dev", "https://aurora.drpc.org"], "decimals": 18, "chainId": **********, "network": null, "contractAddress": null, "explorers": ["https://explorer.aurora.dev/"], "valueUsd": null, "symbol": "AETH", "cgSymbol": "ethereum", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-453c3535ce32", "name": "<PERSON><PERSON>", "color": **********, "type": 0, "icon": "https://cdn.sei.io/sei-app/sei-icon.png", "rpcUrls": ["https://evm-rpc.sei-apis.com"], "decimals": 18, "chainId": 1329, "network": null, "contractAddress": null, "explorers": ["https://seitrace.com/", "https://seistream.app/"], "valueUsd": null, "symbol": "SEI", "cgSymbol": "sei-network", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-453c91822ce33", "name": "Immutable zkEVM", "color": **********, "type": 0, "icon": "https://static.debank.com/image/eth_token/logo_url/******************************************/7a875818146ec0508d3e5f5b14f1b4eb.png", "rpcUrls": ["https://rpc.immutable.com", "https://immutable-zkevm.drpc.org"], "decimals": 18, "chainId": 13371, "network": null, "contractAddress": null, "explorers": ["https://immutable-mainnet.blockscout.com/", "https://explorer.immutable.com/"], "valueUsd": null, "symbol": "IMX", "cgSymbol": "immutable-x", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-45325331ce34", "name": "Sonic", "color": **********, "type": 0, "icon": "https://static.debank.com/image/sonic_token/logo_url/******************************************/b4cc70d040518a88adac18d906fcbfff.png", "rpcUrls": ["https://rpc.soniclabs.com", "https://sonic-rpc.publicnode.com"], "decimals": 18, "chainId": 146, "network": null, "contractAddress": null, "explorers": ["https://explorer.soniclabs.com/", "https://sonicscan.org/"], "valueUsd": null, "symbol": "S", "cgSymbol": "sonic", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-453c93737e35", "name": "Gravity", "color": **********, "type": 0, "icon": "https://assets.gravity.xyz/token_logo.png", "rpcUrls": ["https://rpc.gravity.xyz/"], "decimals": 18, "chainId": 1625, "network": null, "contractAddress": null, "explorers": ["https://explorer.gravity.xyz/"], "valueUsd": null, "symbol": "G", "cgSymbol": "gravity", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-453c937373e36", "name": "<PERSON><PERSON>", "color": **********, "type": 0, "icon": "https://raw.githubusercontent.com/lifinance/types/main/src/assets/icons/chains/taiko.svg", "rpcUrls": ["https://rpc.mainnet.taiko.xyz", "https://taiko-rpc.publicnode.com"], "decimals": 18, "chainId": 167000, "network": null, "contractAddress": null, "explorers": ["https://taikoscan.io/"], "valueUsd": null, "symbol": "ETH", "cgSymbol": "ethereum", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-453c3737ce37", "name": "Soneium", "color": **********, "type": 0, "icon": "https://raw.githubusercontent.com/lifinance/types/main/src/assets/icons/chains/soneium.svg", "rpcUrls": ["https://rpc.soneium.org/"], "decimals": 18, "chainId": 1868, "network": null, "contractAddress": null, "explorers": ["https://soneium.blockscout.com/"], "valueUsd": null, "symbol": "ETH", "cgSymbol": "ethereum", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-453c93636e38", "name": "Swellchain", "color": **********, "type": 0, "icon": "https://raw.githubusercontent.com/lifinance/types/main/src/assets/icons/chains/swell.svg", "rpcUrls": ["https://swell-mainnet.alt.technology", "https://rpc.ankr.com/swell"], "decimals": 18, "chainId": 1923, "network": null, "contractAddress": null, "explorers": ["https://swellchainscan.io/"], "valueUsd": null, "symbol": "ETH", "cgSymbol": "ethereum", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-453c848ce39", "name": "Corn", "color": **********, "type": 0, "icon": "https://static.debank.com/image/corn_token/logo_url/corn/fe230e468272b84aba78d08bb4140456.png", "rpcUrls": ["https://mainnet.corn-rpc.com", "https://rpc.ankr.com/corn_maizenet", "https://maizenet-rpc.usecorn.com"], "decimals": 18, "chainId": ********, "network": null, "contractAddress": null, "explorers": ["https://cornscan.io/"], "valueUsd": null, "symbol": "BTCN", "cgSymbol": "bitcorn", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-453c9232630", "name": "Lens", "color": **********, "type": 0, "icon": "https://s2.coinmarketcap.com/static/img/coins/64x64/23508.png", "rpcUrls": ["https://api.lens.matterhosted.dev", "https://rpc.lens.xyz"], "decimals": 18, "chainId": 232, "network": null, "contractAddress": null, "explorers": ["https://explorer.lens.xyz/"], "valueUsd": null, "symbol": "GHO", "cgSymbol": "gho", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-4532525231ce41", "name": "Cronos", "color": 4278267247, "type": 0, "icon": "https://s2.coinmarketcap.com/static/img/coins/64x64/3635.png", "rpcUrls": ["https://evm.cronos.org", "https://cronos-evm-rpc.publicnode.com", "https://cronos.drpc.org"], "decimals": 18, "chainId": 25, "network": null, "contractAddress": null, "explorers": ["https://explorer.cronos.org/", "https://cronoscan.com/"], "valueUsd": null, "symbol": "CRO", "cgSymbol": "crypto-com-chain", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-453c34636ce42", "name": "<PERSON><PERSON><PERSON>", "color": **********, "type": 0, "icon": "https://assets.coingecko.com/coins/images/28284/standard/frxETH_icon.png", "rpcUrls": ["https://rpc.frax.com"], "decimals": 18, "chainId": 252, "network": null, "contractAddress": null, "explorers": ["https://fraxscan.com/"], "valueUsd": null, "symbol": "frxETH", "cgSymbol": "frax-ether", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-453c2535ce43", "name": "Abstract", "color": **********, "type": 0, "icon": "https://raw.githubusercontent.com/lifinance/types/main/src/assets/icons/chains/abstract.svg", "rpcUrls": ["https://api.mainnet.abs.xyz"], "decimals": 18, "chainId": 2741, "network": null, "contractAddress": null, "explorers": ["https://abscan.org/"], "valueUsd": null, "symbol": "ETH", "cgSymbol": "ethereum", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-453c94474e44", "name": "Rootstock", "color": **********, "type": 0, "icon": "https://static.debank.com/image/rsk_token/logo_url/******************************************/4785a26ef5bb5df987e67ad49fc62137.png", "rpcUrls": ["https://public-node.rsk.co", "https://mycrypto.rsk.co"], "decimals": 18, "chainId": 30, "network": null, "contractAddress": null, "explorers": ["https://explorer.rsk.co/", "https://rootstock.blockscout.com/"], "valueUsd": null, "symbol": "RBTC", "cgSymbol": "rootstock", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-453c93635e45", "name": "<PERSON><PERSON>", "color": **********, "type": 0, "icon": "https://s2.coinmarketcap.com/static/img/coins/64x64/5567.png", "rpcUrls": ["https://forno.celo.org"], "decimals": 18, "chainId": 42220, "network": null, "contractAddress": null, "explorers": ["https://celoscan.io/"], "valueUsd": null, "symbol": "CELO", "cgSymbol": "celo", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-453c93636e46", "name": "World Chain", "color": **********, "type": 0, "icon": "https://raw.githubusercontent.com/lifinance/types/main/src/assets/icons/chains/world.svg", "rpcUrls": ["https://worldchain-mainnet.g.alchemy.com/public", "https://480.rpc.thirdweb.com", "https://worldchain-mainnet.gateway.tenderly.co"], "decimals": 18, "chainId": 480, "network": null, "contractAddress": null, "explorers": ["https://worldscan.org/", "https://worldchain-mainnet.explorer.alchemy.com/"], "valueUsd": null, "symbol": "ETH", "cgSymbol": "ethereum", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-45336331ce47", "name": "Mantle", "color": **********, "type": 0, "icon": "https://static.debank.com/image/mnt_token/logo_url/******************************************/a443c78c33704d48f06e5686bb87f85e.png", "rpcUrls": ["https://rpc.mantle.xyz", "https://mantle-rpc.publicnode.com"], "decimals": 18, "chainId": 5000, "network": null, "contractAddress": null, "explorers": ["https://explorer.mantle.xyz/", "https://mantlescan.xyz/"], "valueUsd": null, "symbol": "MNT", "cgSymbol": "mantle", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-43369231ce48", "name": "Superposition", "color": **********, "type": 0, "icon": "https://raw.githubusercontent.com/lifinance/types/main/src/assets/icons/chains/superposition.svg", "rpcUrls": ["https://rpc.superposition.so"], "decimals": 18, "chainId": 55244, "network": null, "contractAddress": null, "explorers": ["https://explorer.superposition.so/"], "valueUsd": null, "symbol": "ETH", "cgSymbol": "ethereum", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-436369231ce49", "name": "Ink", "color": **********, "type": 0, "icon": "https://raw.githubusercontent.com/lifinance/types/main/src/assets/icons/chains/ink.svg", "rpcUrls": ["https://rpc-gel.inkonchain.com", "https://rpc-qnd.inkonchain.com"], "decimals": 18, "chainId": 57073, "network": null, "contractAddress": null, "explorers": ["https://explorer.inkonchain.com/"], "valueUsd": null, "symbol": "ETH", "cgSymbol": "ethereum", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6be3-616b-4502-af5f-43335331ce50", "name": "<PERSON><PERSON><PERSON><PERSON>", "color": **********, "type": 0, "icon": "https://raw.githubusercontent.com/lifinance/types/main/src/assets/icons/tokens/bera.svg", "rpcUrls": ["https://rpc.berachain.com", "https://berachain-rpc.publicnode.com", "https://rpc.berachain-apis.com"], "decimals": 18, "chainId": 80094, "network": null, "contractAddress": null, "explorers": ["https://berascan.com/", "https://beratrail.io/"], "valueUsd": null, "symbol": "BERA", "cgSymbol": "bera", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-453c94646ce51", "name": "<PERSON><PERSON>", "color": **********, "type": 0, "icon": "https://static.debank.com/image/klay_token/logo_url/******************************************/1866fc790849a903e0594ce0f55b758d.png", "rpcUrls": ["https://public-en.node.kaia.io"], "decimals": 18, "chainId": 8217, "network": null, "contractAddress": null, "explorers": ["https://kaiascan.io/", "https://kaiascope.com/"], "valueUsd": null, "symbol": "KLAY", "cgSymbol": "klaytn", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "42e6b336-616b-4502-af5f-425c9231ce52", "name": "Solana", "color": **********, "type": 0, "icon": "https://s2.coinmarketcap.com/static/img/coins/64x64/5426.png", "rpcUrls": ["https://api.mainnet-beta.solana.com", "https://solana-rpc.publicnode.com"], "decimals": 9, "chainId": ****************, "network": null, "contractAddress": null, "explorers": ["https://solana.fm/", "https://explorer.solana.com/", "https://solscan.io/"], "valueUsd": 0.0, "symbol": "SOL", "cgSymbol": "solana", "networkType": 1, "refToken": "********************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "bob-network6b336e-616b-4502-af5f-453c9h4646ce51", "name": "ETH", "color": 4293490176, "type": 0, "icon": "https://raw.githubusercontent.com/lifinance/types/main/src/assets/icons/chains/bob.svg", "rpcUrls": ["https://rpc.gobob.xyz", "https://bob-mainnet.public.blastapi.io"], "decimals": 18, "chainId": 60808, "network": null, "contractAddress": null, "explorers": ["https://explorer.gobob.xyz"], "valueUsd": null, "symbol": "BOB", "cgSymbol": "ethereum", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "apechaine6b336e-616b-4502-af5f-453c9h4646ce51", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "color": 4278213347, "type": 0, "icon": "https://raw.githubusercontent.com/lifinance/types/main/src/assets/icons/chains/apechain.svg", "rpcUrls": ["https://rpc.apechain.com"], "decimals": 18, "chainId": 33139, "network": null, "contractAddress": null, "explorers": ["https://apescan.io"], "valueUsd": null, "symbol": "APE", "cgSymbol": "a<PERSON><PERSON>n", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}, {"canDisplay": false, "cryptoId": "xdcey6b336-616b-4502-af5f-453c94646ce51", "name": "XCD Network", "color": 4278206089, "type": 0, "icon": "https://raw.githubusercontent.com/lifinance/types/main/src/assets/icons/chains/xdc.svg", "rpcUrls": ["https://erpc.xinfin.network", "https://rpc.xinfin.network", "https://rpc1.xinfin.network", "https://rpc.xdcrpc.com", "https://erpc.xdcrpc.com", "https://rpc.ankr.com/xdc", "https://rpc.xdc.org"], "decimals": 18, "chainId": 50, "network": null, "contractAddress": null, "explorers": ["https://xdcscan.com/", "https://xdcscan.io/"], "valueUsd": null, "symbol": "XDC", "cgSymbol": "xdce-crowd-sale", "networkType": 0, "refToken": "******************************************", "refTokenChainId": null}]